#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ssl
import urllib3
import warnings
import os
import asyncio

# 彻底禁用SSL验证
ssl._create_default_https_context = ssl._create_unverified_context

# 禁用警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
warnings.filterwarnings('ignore', message='Unverified HTTPS request')

# 设置环境变量
os.environ['PYTHONHTTPSVERIFY'] = '0'
os.environ['SSL_VERIFY'] = 'false'
os.environ['CURL_CA_BUNDLE'] = ''
os.environ['REQUESTS_CA_BUNDLE'] = ''
os.environ['HF_TOKEN'] = '*************************************'

# 修补aiohttp SSL验证
import aiohttp
original_connector = aiohttp.TCPConnector

class InsecureConnector(aiohttp.TCPConnector):
    def __init__(self, *args, **kwargs):
        kwargs['ssl'] = False
        super().__init__(*args, **kwargs)

# 全局替换
aiohttp.TCPConnector = InsecureConnector

# 修补requests
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 创建不验证SSL的session
session = requests.Session()
session.verify = False

# 应用到所有requests
requests.Session.verify = False
requests.adapters.DEFAULT_RETRIES = 3

print("🔧 SSL绕过配置已完成")
print(f"✅ HF_TOKEN已设置: {os.environ.get('HF_TOKEN', 'Not set')[:20]}...")