#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ssl
import urllib3
import warnings
import os
import aiohttp
import asyncio

# 禁用所有SSL相关警告
urllib3.disable_warnings()
warnings.filterwarnings('ignore')

# 彻底禁用SSL验证
ssl._create_default_https_context = ssl._create_unverified_context

# 设置环境变量
os.environ['PYTHONHTTPSVERIFY'] = '0'
os.environ['SSL_VERIFY'] = 'false'
os.environ['CURL_CA_BUNDLE'] = ''
os.environ['REQUESTS_CA_BUNDLE'] = ''

# 猴子补丁aiohttp
original_aiohttp_connector = aiohttp.TCPConnector

class UnsafeConnector(aiohttp.TCPConnector):
    def __init__(self, *args, **kwargs):
        kwargs['ssl'] = False
        super().__init__(*args, **kwargs)

aiohttp.TCPConnector = UnsafeConnector

# 修补requests
import requests
requests.packages.urllib3.disable_warnings()

# 创建一个不验证SSL的session
session = requests.Session()
session.verify = False

# 修补g4f中的请求方法
try:
    import g4f.requests
    original_get = g4f.requests.get
    original_post = g4f.requests.post
    
    def patched_get(*args, **kwargs):
        kwargs['verify'] = False
        return original_get(*args, **kwargs)
    
    def patched_post(*args, **kwargs):
        kwargs['verify'] = False
        return original_post(*args, **kwargs)
    
    g4f.requests.get = patched_get
    g4f.requests.post = patched_post
except:
    pass

print("🔧 SSL绕过配置已完成")

# 现在测试
from g4f.client import Client

client = Client()
print("✅ 客户端创建成功")

# 测试本地可用的提供商
providers_to_try = [
    "Chatai",
    "TeachAnything", 
    "WeWordle",
    "Yqcloud"
]

for provider_name in providers_to_try:
    try:
        print(f"🔄 测试提供商: {provider_name}")
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "你好，请简单回答"}],
            provider=provider_name
        )
        print(f"✅ {provider_name} 成功!")
        print(f"回复: {response.choices[0].message.content}")
        break
    except Exception as e:
        print(f"❌ {provider_name} 失败: {str(e)[:100]}...")
        continue

print("\n🚀 尝试启动GUI...")