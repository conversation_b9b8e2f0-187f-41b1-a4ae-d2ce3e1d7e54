name: Publish Python 🐍 distribution 📦 to PyPI

on: push

env:
  G4F_VERSION: ${{ github.ref_name }}

jobs:
  build:
    name: Build distribution 📦
    if: github.repository == 'xtekky/gpt4free' && startsWith(github.ref, 'refs/tags/')
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.x"
    - name: Install pypa/build
      run: >-
        python3 -m
        pip install
        build
        --user
    - name: Build a binary wheel and a source tarball
      run: python3 -m build
    - name: Store the distribution packages
      uses: actions/upload-artifact@v4
      with:
        name: python-package-distributions
        path: dist/

  publish-to-pypi:
    name: >-
      Publish distribution on PyPI 🐍
    if: startsWith(github.ref, 'refs/tags/')
    needs:
    - build
    runs-on: ubuntu-latest
    environment:
      name: pypi
      url: https://pypi.org/p/g4f
    permissions:
      id-token: write
    steps:
    - name: Download all the dists
      uses: actions/download-artifact@v4
      with:
        name: python-package-distributions
        path: dist/
    - name: Publish distribution 📦 to PyPI
      uses: pypa/gh-action-pypi-publish@release/v1