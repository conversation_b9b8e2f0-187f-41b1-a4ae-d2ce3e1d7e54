name: Unittest

on: 
  pull_request:
    types:
      - opened
      - synchronize
  push:
    branches:
      - 'main'

jobs:
  build:
    name: Build unittest
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.8
      uses: actions/setup-python@v4
      with:
        python-version: "3.8"
        cache: 'pip'
    - name: Install min requirements
      run: pip install -r requirements-min.txt
    - name: Run tests
      run: python -m etc.unittest
    - name: Set up Python 3.12
      uses: actions/setup-python@v4
      with:
        python-version: "3.12"
        cache: 'pip'
    - name: Install requirements
      run: |
        pip install -r requirements.txt
        pip uninstall -y nodriver
    - name: Run tests
      run: python -m etc.unittest
    - name: Save PR number
      env:
        PR_NUMBER: ${{ github.event.number }}
      run: |
        mkdir -p ./pr
        echo $PR_NUMBER > ./pr/pr_number
    - uses: actions/upload-artifact@v4
      with:
        name: pr_number
        path: pr/