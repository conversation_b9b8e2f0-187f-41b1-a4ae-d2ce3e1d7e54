---
name: Bug Report
about: Report issues with specific models/providers
title: '[Bug] '
labels: bug
assignees: hlohaus
---

**Before submitting**
☑️ I checked [Known Issues](https://github.com/xtekky/gpt4free#known-issues)  
☑️ I searched existing issues

**Configuration**  
*(Required)*
- Model:**
- Provider:**
- Interface:** (CLI/API/Web UI/Other)

**Bug description**
Clear steps to reproduce:
1. Exact command/file used:  
   `python ...`
2. Environment location:  
   (e.g., `Germany` - Cloudflare restrictions may apply)
3. Observed behavior vs expected:  

**Environment**
- Python version: `python --version`
- OS: (Windows 11/Ubuntu 22.04/macOS Ventura)
- Relevant dependencies: `pip freeze | grep -E 'g4f|requests'`

**Screenshots/Logs**
Copy&Paste terminal output or error logs here


**Additional context**
Proxy/VPN usage? Special config? Other providers working?