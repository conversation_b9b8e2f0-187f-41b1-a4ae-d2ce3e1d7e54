FROM python:slim-bookworm

ARG G4F_VERSION
ARG G4F_LIVE=true
ARG G4F_USER=g4f
ARG G4F_USER_ID=1000

ENV G4F_VERSION $G4F_VERSION
ENV G4F_LIVE $G4F_LIVE
ENV G4F_USER $G4F_USER
ENV G4F_USER_ID $G4F_USER_ID
ENV G4F_DIR /app

RUN apt-get update && apt-get upgrade -y \
  && apt-get install -y git cron ffmpeg flac \
# Add user and user group
  && groupadd -g $G4F_USER_ID $G4F_USER \
  && useradd -rm -G sudo -u $G4F_USER_ID -g $G4F_USER_ID $G4F_USER \
  && echo "${G4F_USER}:${G4F_USER}" | chpasswd \
  && python -m pip install --upgrade pip \
  && apt-get clean \
  && rm --recursive --force /var/lib/apt/lists/* /tmp/* /var/tmp/*

RUN mkdir -p $G4F_DIR
RUN chown "${G4F_USER_ID}:${G4F_USER_ID}" $G4F_DIR

USER $G4F_USER_ID
WORKDIR $G4F_DIR

ENV HOME /home/<USER>
ENV PATH "${HOME}/.local/bin:${PATH}"

RUN git clone https://github.com/xtekky/gpt4free.git $G4F_DIR \
 && pip install --no-cache-dir -r requirements-slim.txt
RUN git clone https://github.com/hlohaus/deepseek4free.git \
 && cd deepseek4free && git checkout 21Feb \
 && pip install --no-cache-dir . && cd .. && rm -rf deepseek4free

CMD python -m etc.tool.update && docker/update.sh & docker/start.sh