from __future__ import annotations

import os
import logging
from typing import Union, Optional, Coroutine

# 检查是否启用本地模式
if os.environ.get('G4F_LOCAL_ONLY', 'false').lower() == 'true':
    print("🔒 GPT4Free initialized in local-only mode")

from . import debug, version
from .models import Model
from .client import Client, AsyncClient
from .typing import Messages, CreateResult, AsyncResult, ImageType
from .cookies import get_cookies, set_cookies
from .providers.types import ProviderType
from .providers.helper import concat_chunks, async_concat_chunks
from .client.service import get_model_and_provider

#Configure "g4f" logger
logger = logging.getLogger(__name__)
log_handler = logging.StreamHandler()
log_handler.setFormatter(logging.Formatter(logging.BASIC_FORMAT))
logger.addHandler(log_handler)

logger.setLevel(logging.ERROR)

class ChatCompletion:
    @staticmethod
    def create(model    : Union[Model, str],
               messages : Messages,
               provider : Union[ProviderType, str, None] = None,
               stream   : bool = False,
               image    : ImageType = None,
               image_name: Optional[str] = None,
               ignore_working: bool = False,
               ignore_stream: bool = False,
               **kwargs) -> Union[CreateResult, str]:
        if image is not None:
            kwargs["media"] = [(image, image_name)]
        elif "images" in kwargs:
            kwargs["media"] = kwargs.pop("images")
        model, provider = get_model_and_provider(
            model, provider, stream,
            ignore_working,
            ignore_stream,
            has_images="media" in kwargs,
        )
        if "proxy" not in kwargs:
            proxy = os.environ.get("G4F_PROXY")
            if proxy:
                kwargs["proxy"] = proxy
        if ignore_stream:
            kwargs["ignore_stream"] = True

        result = provider.create_function(model, messages, stream=stream, **kwargs)

        return result if stream or ignore_stream else concat_chunks(result)

    @staticmethod
    def create_async(model    : Union[Model, str],
                     messages : Messages,
                     provider : Union[ProviderType, str, None] = None,
                     stream   : bool = False,
                     image    : ImageType = None,
                     image_name: Optional[str] = None,
                     ignore_stream: bool = False,
                     ignore_working: bool = False,
                     **kwargs) -> Union[AsyncResult, Coroutine[str]]:
        if image is not None:
            kwargs["media"] = [(image, image_name)]
        elif "images" in kwargs:
            kwargs["media"] = kwargs.pop("images")
        model, provider = get_model_and_provider(model, provider, False, ignore_working, has_images="media" in kwargs)
        if "proxy" not in kwargs:
            proxy = os.environ.get("G4F_PROXY")
            if proxy:
                kwargs["proxy"] = proxy
        if ignore_stream:
            kwargs["ignore_stream"] = True

        result = provider.async_create_function(model, messages, stream=stream, **kwargs)

        if not stream and not ignore_stream:
            if hasattr(result, "__aiter__"):
                result = async_concat_chunks(result)

        return result