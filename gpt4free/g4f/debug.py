import sys
import os
from typing import Callable, List, Optional, Any

logging: bool = False
# 检查环境变量，默认禁用版本检查
version_check: bool = os.environ.get('G4F_DISABLE_VERSION_CHECK', 'true').lower() != 'true'
version: Optional[str] = None
log_handler: Callable = print  # More specifically: Callable[[Any, Optional[Any]], None]
logs: List[str] = []

def log(*text: Any, file: Optional[Any] = None) -> None:
    """Log a message if logging is enabled."""
    if logging:
        log_handler(*text, file=file)

def error(*error: Any, name: Optional[str] = None) -> None:
    """Log an error message to stderr."""
    # 在本地模式下减少错误输出
    if os.environ.get('G4F_LOCAL_ONLY', 'false').lower() == 'true':
        return  # 本地模式下忽略网络相关错误
    error = [e if isinstance(e, str) else f"{type(e).__name__ if name is None else name}: {e}" for e in error]
    log(*error, file=sys.stderr)
