audio_models = ['PollinationsAI:gpt-4o-audio', 'PollinationsAI:hypnosis-tracy', 'PollinationsAI:openai-audio', 'PollinationsAI:gpt-4o-mini-audio', 'openai-audio', 'hypnosis-tracy', 'gpt-4o-mini-audio', 'gpt-4o-audio']
image_models = ['dall-e-3', 'PollinationsAI:flux', 'PollinationsAI:turbo', 'PollinationsAI:kontext', 'PollinationsAI:gptimage', 'PollinationsAI:transparent', 'flux', 'turbo', 'kontext', 'gptimage', 'transparent', '', 'black-forest-labs/FLUX.1-dev', 'black-forest-labs/FLUX.1-schnell', 'stabilityai/stable-diffusion-xl-base-1.0', 'flux-dev', 'flux-schnell', 'stable-diffusion-xl-base-1.0', 'black-forest-labs/FLUX.1-dev', 'stabilityai/stable-diffusion-xl-base-1.0', 'black-forest-labs/FLUX.1-schnell', 'stabilityai/stable-diffusion-3.5-large', 'HiDream-ai/HiDream-I1-Full', 'fofr/sdxl-emoji', 'stabilityai/stable-diffusion-3.5-medium', 'ByteDance/Hyper-SD', 'playgroundai/playground-v2.5-1024px-aesthetic', 'renderartist/classic-painting-flux', 'stabilityai/stable-diffusion-3-medium', 'multimodalart/reachy', 'nerijs/pixel-art-xl', 'Norod78/Flux_1_Dev_LoRA_Paper-Cutout-Style', 'alvdansen/flux_film_foto', 'renderartist/simplevectorflux', 'Efficient-Large-Model/Sana_1600M_1024px', 'strangerzonehf/Flux-Super-Realism-LoRA', 'ByteDance/SDXL-Lightning', 'linoyts/yarn_art_Flux_LoRA', 'multimodalart/flux-tarot-v1', 'aleksa-codes/flux-ghibsky-illustration', 'dvyio/flux-lora-film-noir', 'kudzueye/boreal-flux-dev-v2', 'prithivMLmods/Canopus-Cute-Kawaii-Flux-LoRA', 'VinitT/Flux.1_Lora-LightningMcQueen', 'renderartist/coloringbookflux', 'gokaygokay/Flux-Game-Assets-LoRA-v2', 'Keltezaa/sophie-turner-flux', 'prithivMLmods/Ton618-Tarot-Cards-Flux-LoRA', 'ali-vilab/In-Context-LoRA', 'prithivMLmods/Flux.1-Dev-Poster-HQ-LoRA', 'strangerzonehf/Flux-Midjourney-Mix2-LoRA', 'ysmikey/Layerpano3D-FLUX-Panorama-LoRA', 'Keltezaa/alexis-bledel-flux', 'Efficient-Large-Model/SANA1.5_4.8B_1024px_diffusers', 'HiDream-ai/HiDream-I1-Dev', 'HiDream-ai/HiDream-I1-Fast', 'multimodalart/isometric-skeumorphic-3d-bnb', 'renderartist/technically-color-flux', 'CiroN2022/ascii-art', 'Pclanglais/TintinIA', 'artificialguybr/StickersRedmond', 'KappaNeuro/color-palette', 'KappaNeuro/video-installation', 'latent-consistency/lcm-lora-sdxl', 'ming-yang/sdxl_chinese_ink_lora', 'e-n-v-y/envy-anime-watercolor-xl-01', 'artificialguybr/filmgrain-redmond-filmgrain-lora-for-sdxl', 'e-n-v-y/envy-kyotopunk-xl-01', 'ntc-ai/SDXL-LoRA-slider.unreal-engine', 'Norod78/SDXL-Psychemelt-style-LoRA', 'h1t/TCD-SDXL-LoRA', 'artificialguybr/selfiephotographyredmond-selfie-photography-lora-for-sdxl', 'Blib-la/caricature_lora_sdxl', 'lora-library/B-LoRA-watercolor', 'alvdansen/Painted-illustration', 'op74185/watercolor-illustration', 'XLabs-AI/flux-RealismLora', 'alvdansen/frosting_lane_flux', 'martintomov/retrofuturism-flux', 'lucataco/ReplicateFluxLoRA', 'alvdansen/softserve_anime', 'fofr/flux-80s-cyberpunk', 'alvdansen/flux-koda', 'AlloReview/flux-lora-undraw', 'alvdansen/plushy-world-flux', 'nerijs/animation2k-flux', 'rorito/testSCG-Anatomy-Flux1', 'lichorosario/flux-cubist-cartoon', 'mrcuddle/male_climax', 'davisbro/flux-multi-angle', 'Fihade/Kento-IIDA-Retro-Avatar-xl', 'adirik/flux-cinestill', 'jakedahn/flux-latentpop', 'Fihade/Exquisite-illustration-xl', 'playboy40k/flux-TaylorSwiftLora', 'brushpenbob/flux-midjourney-anime', 'punzel/flux_emma_watson', 'oshtz/flux-plastic3d', 'roelfrenkema/flux1.lora.aurora', 'Shakker-Labs/FLUX.1-dev-LoRA-live-3D', 'Shakker-Labs/FLUX.1-dev-LoRA-MiaoKa-Yarn-World', 'prithivMLmods/Canopus-Pixar-3D-Flux-LoRA', 'Shakker-Labs/FLUX.1-dev-LoRA-Logo-Design', 'Shakker-Labs/FLUX.1-dev-LoRA-Children-Simple-Sketch', 'Shakker-Labs/FLUX.1-dev-LoRA-Micro-landscape-on-Mobile-Phone', 'Shakker-Labs/FLUX.1-dev-LoRA-AntiBlur', 'diabolic6045/Flux_Wallpaper_Lora', 'alvdansen/pola-photo-flux', 'bingbangboom/flux_geopop', 'glif/Brain-Melt-Acid-Art', 'playboy40k/flux-SydneySweeneyLora', 'UmeAiRT/FLUX.1-dev-LoRA-Modern_Pixel_art', 'UmeAiRT/FLUX.1-dev-LoRA-Ume_Sky', 'jeremytai/techlinedrawing', 'diabolic6045/Flux_Sticker_Lora', 'Shakker-Labs/FLUX.1-dev-LoRA-One-Click-Creative-Template', 'Purz/vhs-box', 'renderartist/toyboxflux', 'longnthgmedia/flux_lora_meme_v2', 'glif-loradex-trainer/AP123_flux_dev_2DHD_pixel_art', 'bingbangboom/flux-miniature-worlds', 'glif-loradex-trainer/araminta_k_flux_dev_illustration_art', 'prithivMLmods/Castor-Gta6-Theme-Flux-LoRA', 'chau9ho/dolby-flux', 'Keltezaa/scarlett-johansson-2003-flux', 'Keltezaa/flux-alexandra-daddario', 'Keltezaa/lindsay-lohan-actress-2006-flux', 'Keltezaa/gal-gadot-flux', 'Keltezaa/amanda-seyfried-flux', 'Keltezaa/emily-blunt-flux', 'Keltezaa/little-caprice-flux-adult-film-actress', 'Keltezaa/ella-purnell-flux', 'Keltezaa/flux-miley-cyrus', 'Keltezaa/kirsten-dunst-actress-2000s-flux', 'Keltezaa/sarah-michelle-gellar-flux-model', 'Keltezaa/britney-spears-flux-model', 'Keltezaa/flux-taylor-swift', 'prithivMLmods/Castor-Collage-Dim-Flux-LoRA', 'Keltezaa/elle-fanning', 'xey/sldr_flux_nsfw_v2-studio', 'prithivMLmods/Castor-3D-Portrait-Flux-LoRA', 'Keltezaa/alicia-vikander-sdxl-flux', 'prithivMLmods/Ton618-Space-Wallpaper-LoRA', 'prithivMLmods/Ton618-Only-Stickers-Flux-LoRA', 'prithivMLmods/Minimal-Futuristic-Flux-LoRA', 'tryonlabs/FLUX.1-dev-LoRA-Outfit-Generator', 'gokaygokay/Flux-2D-Game-Assets-LoRA', 'prithivMLmods/Red-Undersea-Flux-LoRA', 'Keltezaa/anna-kendrick-flux', 'Keltezaa/eva-green-flux', 'Keltezaa/movie-poster-ce-sdxl-flux', 'prithivMLmods/Retro-Pixel-Flux-LoRA', 'strangerzonehf/Flux-Ultimate-LoRA-Collection', 'gokaygokay/Flux-Digital-Backgrounds-LoRA', 'prithivMLmods/Flux-Long-Toon-LoRA', 'strangerzonehf/Flux-Isometric-3D-LoRA', 'strangerzonehf/Flux-Cute-3D-Kawaii-LoRA', 'Keltezaa/zooey-deschanel', 'prithivMLmods/Flux-Lego-Ref-LoRA', 'strangerzonehf/Flux-NFTv4-Designs-LoRA', 'strangerzonehf/Flux-Claymation-XC-LoRA', 'strangerzonehf/Flux-Microworld-NFT-LoRA', 'Keltezaa/megan-fox-flux', 'mrcuddle/live2d-model-maker', 'playboy40k/flux-DuaLipaLora', 'playboy40k/flux-SabrinaCarpenterLora', 'gokaygokay/Flux-Watercolor-Strokes-LoRA', 'strangerzonehf/Flux-Sketch-Smudge-LoRA', 'Keltezaa/yvonne-strahovski-flux', 'Keltezaa/katie-fey-jenya-d-eugenia-diordiychuk', 'Keltezaa/olivia-wilde-flux', 'Keltezaa/kristen-bell', 'Keltezaa/avril-lavigne-2000s-flux-lora', 'Keltezaa/selena-gomez-2012-flux', 'uriel353/leah-gotti', 'uriel353/jessica-chastain-flux', 'strangerzonehf/2Color-Illustration', 'strangerzonehf/cinematicShot-Pics-Flux', 'Keltezaa/Foxy_Di', 'Shakker-Labs/FLUX.1-dev-LoRA-Miniature-World', 'DavidBaloches/Extreme_Detailer', 'Keltezaa/annie-edison-community-flux1-d', 'strangerzonehf/Real-Claymation', 'Efficient-Large-Model/SANA1.5_4.8B_1024px', 'Datou1111/Slow-Shutter', 'Jonjew/ErikaEleniak', 'salomonsky/flux-lora-uncensored', 'Jonjew/ThePoseProneWithFeetUp', 'strangerzonehf/Flux-Midjourney-Painterly-LoRA', 'Jonjew/LindsayLohanMeanGirls', 'WizWhite/wizard-s-popcore-illustration', 'Viktor1717/scandinavian-interior-style1', 'Ghettolametto/LORA', 'Seryoger/Parique_v1', 'Seryoger/Tommy', 'openfree/flux-chatgpt-ghibli-lora', 'openfree/van-gogh', 'enhanceaiteam/Flux-uncensored', 'lustlyai/Flux_Lustly.ai_Uncensored_nsfw_v1', 's2fcqj-org/remove-clothes', 'CultriX/flux-nsfw-highress', 'Keltezaa/Celeb_anatomy_female_v2', 'Keltezaa/Fingering', 'Keltezaa/Bodywriting_Flux', 'Keltezaa/blowjob-pov-flux-lora', 'Jonny001/Anime-Esil-Radiru', 'uriel353/photorealistic-nsfw', 'Jonjew/FeetFetish', 'DRDELATV/LORA_ASIAN_FASHION', 'AI-Porn/pornworks-characters-tifa-lockhart-sdxl', 'flux-dev', 'stable-diffusion-xl-base-1.0', 'flux-schnell', 'sd-3.5-large', 'hidream-i1-full', 'sdxl-emoji', 'stable-diffusion-3.5-medium', 'hyper-sd', 'playground-v2.5-1024px-aesthetic', 'classic-painting-flux', 'stable-diffusion-3-medium', 'reachy', 'pixel-art-xl', 'flux.1.dev.lora.paper-cutout-style', 'flux.film.foto', 'simplevectorflux', 'sana.1600m.1024px', 'flux-super-realism-lora', 'sdxl-lightning', 'yarn.art.flux.lora', 'flux-tarot', 'flux-ghibsky-illustration', 'flux-lora-film-noir', 'boreal-flux-dev', 'canopus-cute-kawaii-flux-lora', 'flux.1.lora-lightningmcqueen', 'coloringbookflux', 'flux-game-assets-lora', 'sophie-turner-flux', 'ton618-tarot-cards-flux-lora', 'in-context-lora', 'flux-dev-poster-hq-lora', 'flux-midjourney-mix2-lora', 'layerpano3d-flux-panorama-lora', 'alexis-bledel-flux', 'sana1.5.4.8b.1024px.diffusers', 'hidream-i1-dev', 'hidream-i1-fast', 'isometric-skeumorphic-3d-bnb', 'technically-color-flux', 'ascii-art', 'tintinia', 'stickersredmond', 'color-palette', 'video-installation', 'lcm-lora-sdxl', 'sdxl.chinese.ink.lora', 'envy-anime-watercolor-xl-01', 'filmgrain-redmond-filmgrain-lora-for-sdxl', 'envy-kyotopunk-xl-01', 'sdxl-lora-slider.unreal-engine', 'sdxl-psychemelt-style-lora', 'tcd-sdxl-lora', 'selfiephotographyredmond-selfie-photography-lora-for-sdxl', 'caricature.lora.sdxl', 'b-lora-watercolor', 'painted-illustration', 'watercolor-illustration', 'flux-realismlora', 'frosting.lane.flux', 'retrofuturism-flux', 'replicatefluxlora', 'softserve.anime', 'flux-80s-cyberpunk', 'flux-koda', 'flux-lora-undraw', 'plushy-world-flux', 'animation2k-flux', 'testscg-anatomy-flux1', 'flux-cubist-cartoon', 'male.climax', 'flux-multi-angle', 'kento-iida-retro-avatar-xl', 'flux-cinestill', 'flux-latentpop', 'exquisite-illustration-xl', 'flux-taylorswiftlora', 'flux-midjourney-anime', 'flux.emma.watson', 'flux-plastic3d', 'flux1.lora.aurora', 'flux-dev-lora-live-3d', 'flux-dev-lora-miaoka-yarn-world', 'canopus-pixar-3d-flux-lora', 'flux-dev-lora-logo-design', 'flux-dev-lora-children-simple-sketch', 'flux-dev-lora-micro-landscape-on-mobile-phone', 'flux-dev-lora-antiblur', 'flux.wallpaper.lora', 'pola-photo-flux', 'flux.geopop', 'brain-melt-acid-art', 'flux-sydneysweeneylora', 'flux-dev-lora-modern.pixel.art', 'flux-dev-lora-ume.sky', 'techlinedrawing', 'flux.sticker.lora', 'flux-dev-lora-one-click-creative-template', 'vhs-box', 'toyboxflux', 'flux.lora.meme.v2', 'ap123.flux.dev.2dhd.pixel.art', 'flux-miniature-worlds', 'araminta.k.flux.dev.illustration.art', 'castor-gta6-theme-flux-lora', 'dolby-flux', 'scarlett-johansson-2003-flux', 'flux-alexandra-daddario', 'lindsay-lohan-actress-2006-flux', 'gal-gadot-flux', 'amanda-seyfried-flux', 'emily-blunt-flux', 'little-caprice-flux-adult-film-actress', 'ella-purnell-flux', 'flux-miley-cyrus', 'kirsten-dunst-actress-2000s-flux', 'sarah-michelle-gellar-flux-model', 'britney-spears-flux-model', 'flux-taylor-swift', 'castor-collage-dim-flux-lora', 'elle-fanning', 'sldr.flux.nsfw.v2-studio', 'castor-3d-portrait-flux-lora', 'alicia-vikander-sdxl-flux', 'ton618-space-wallpaper-lora', 'ton618-only-stickers-flux-lora', 'minimal-futuristic-flux-lora', 'flux-dev-lora-outfit-generator', 'flux-2d-game-assets-lora', 'red-undersea-flux-lora', 'anna-kendrick-flux', 'eva-green-flux', 'movie-poster-ce-sdxl-flux', 'retro-pixel-flux-lora', 'flux-ultimate-lora-collection', 'flux-digital-backgrounds-lora', 'flux-long-toon-lora', 'flux-isometric-3d-lora', 'flux-cute-3d-kawaii-lora', 'zooey-deschanel', 'flux-lego-ref-lora', 'flux-nftv4-designs-lora', 'flux-claymation-xc-lora', 'flux-microworld-nft-lora', 'megan-fox-flux', 'live2d-model-maker', 'flux-dualipalora', 'flux-sabrinacarpenterlora', 'flux-watercolor-strokes-lora', 'flux-sketch-smudge-lora', 'yvonne-strahovski-flux', 'katie-fey-jenya-d-eugenia-diordiychuk', 'olivia-wilde-flux', 'kristen-bell', 'avril-lavigne-2000s-flux-lora', 'selena-gomez-2012-flux', 'leah-gotti', 'jessica-chastain-flux', '2color-illustration', 'cinematicshot-pics-flux', 'foxy.di', 'flux-dev-lora-miniature-world', 'extreme.detailer', 'annie-edison-community-flux1-d', 'real-claymation', 'sana1.5.4.8b.1024px', 'slow-shutter', 'erikaeleniak', 'flux-lora-uncensored', 'theposepronewithfeetup', 'flux-midjourney-painterly-lora', 'lindsaylohanmeangirls', 'wizard-s-popcore-illustration', 'scandinavian-interior-style1', 'lora', 'parique.v1', 'tommy', 'flux-chatgpt-ghibli-lora', 'van-gogh', 'flux-uncensored', 'flux.lustly.ai.uncensored.nsfw.v1', 'remove-clothes', 'flux-nsfw-highress', 'celeb.anatomy.female.v2', 'fingering', 'bodywriting.flux', 'blowjob-pov-flux-lora', 'anime-esil-radiru', 'photorealistic-nsfw', 'feetfetish', 'lora.asian.fashion', 'pornworks-characters-tifa-lockhart-sdxl', 'flux-1-kontext-pro', 'gpt-image-1', 'flux-1-kontext-max', 'imagen-4.0-ultra-generate-preview-06-06', 'imagen-3.0-generate-002', 'ideogram-v2', 'photon', 'step1x-edit', 'dall-e-3', 'recraft-v3', 'anonymous-bot-0514', 'flux-1.1-pro', 'ideogram-v3-quality', 'imagen-4.0-generate-preview-06-06', 'seedream-3', 'seededit-3.0', 'flux-1-kontext-dev', 'bagel', 'gemini-2.0-flash-preview-image-generation', 'flux-1-kontext-pro', 'gpt-image-1', 'flux-1-kontext-max', 'imagen-4.0-ultra-generate', 'imagen-3.0-generate-002', 'ideogram', 'photon', 'step1x-edit', 'dall-e-3', 'recraft', 'anonymous-bot-0514', 'flux-1.1-pro', 'ideogram-v3-quality', 'imagen-4.0-generate', 'seedream-3', 'seededit-3.0', 'flux-1-kontext-dev', 'bagel', 'gemini-2.0-flash-preview-image-generation', 'flux', 'janus-pro-7b-image', 'sd-3.5-large', 'flux-dev', 'flux-kontext-dev', 'flux', 'janus-pro-7b-image', 'sd-3.5-large', 'flux-dev', 'flux-kontext-dev']
vision_models = ['auto', 'gpt-4', 'gpt-4.1', 'gpt-4.1-mini', 'gpt-4.5', 'gpt-4o', 'gpt-4o-mini', 'o1', 'o1-mini', 'o3-mini', 'o3-mini-high', 'o4-mini', 'o4-mini-high', 'PollinationsAI:openai', 'PollinationsAI:evil', 'PollinationsAI:llama-fast-roblox', 'PollinationsAI:mistral-small-3.1-24b', 'PollinationsAI:mistral-roblox', 'PollinationsAI:gpt-4o-mini', 'PollinationsAI:gpt-4o-audio', 'PollinationsAI:gpt-4.1-nano', 'PollinationsAI:gpt-4.1', 'PollinationsAI:o4-mini', 'PollinationsAI:openai-roblox', 'PollinationsAI:phi-4', 'PollinationsAI:bidara', 'PollinationsAI:mirexa', 'PollinationsAI:sur', 'PollinationsAI:unity', 'openai', 'llama-fast-roblox', 'mistral-small-3.1-24b', 'mistral-roblox', 'gpt-4o-mini', 'gpt-4o-audio', 'gpt-4.1-nano', 'gpt-4.1', 'o4-mini', 'openai-roblox', 'phi-4', 'bidara', 'evil', 'mirexa', 'sur', 'unity', 'gpt-4', 'gpt-4o', 'gpt-4.1-mini', 'gemini-2.5-pro-preview-05-06', 'o3-2025-04-16', 'o4-mini-2025-04-16', 'mistral-medium-2505', 'gemini-2.5-flash-preview-04-17', 'gpt-4.1-2025-04-14', 'claude-3-7-sonnet-20250219', 'claude-3-7-sonnet-20250219-thinking-32k', 'llama-4-maverick-17b-128e-instruct', 'llama-4-scout-17b-16e-instruct', 'gpt-4.1-mini-2025-04-14', 'gpt-4.1-nano-2025-04-14', 'gemini-2.0-flash-001', 'gemini-2.0-flash-lite-preview-02-05', 'gemma-3-27b-it', 'claude-3-5-sonnet-20241022', 'gpt-4o-mini-2024-07-18', 'gpt-4o-2024-11-20', 'gpt-4o-2024-08-06', 'gpt-4o-2024-05-13', 'mistral-small-3.1-24b-instruct-2503', 'claude-3-5-sonnet-20240620', 'amazon-nova-pro-v1.0', 'amazon-nova-lite-v1.0', 'qwen2.5-vl-32b-instruct', 'qwen2.5-vl-72b-instruct', 'gemini-1.5-pro-002', 'gemini-1.5-flash-002', 'gemini-1.5-flash-8b-001', 'gemini-1.5-pro-001', 'gemini-1.5-flash-001', 'pixtral-large-2411', 'step-1o-vision-32k-highres', 'claude-3-haiku-20240307', 'claude-3-sonnet-20240229', 'claude-3-opus-20240229', 'qwen-vl-max-1119', 'qwen-vl-max-0809', 'reka-core-20240904', 'reka-flash-20240904', 'c4ai-aya-vision-32b', 'pixtral-12b-2409', 'gemini-2.5-pro', 'o3', 'o4-mini', 'mistral-medium-2505', 'gemini-2.5-flash', 'gpt-4.1', 'claude-3-7-sonnet', 'claude-3-7-sonnet-20250219-thinking-32k', 'llama-4-maverick-17b-128e', 'llama-4-scout-17b-16e', 'gpt-4.1-mini', 'gpt-4.1-nano', 'gemini-2.0-flash-001', 'gemini-2.0-flash-lite', 'gemma-3-27b-it', 'claude-3-5-sonnet', 'gpt-4o-mini', 'gpt-4o', 'gpt-4o', 'gpt-4o', 'mistral-small-3.1-24b-instruct-2503', 'claude-3-5-sonnet', 'amazon-nova-pro-v1.0', 'amazon-nova-lite-v1.0', 'qwen2.5-vl-32b', 'qwen2.5-vl-72b', 'gemini-1.5-pro-002', 'gemini-1.5-flash-002', 'gemini-1.5-flash-8b-001', 'gemini-1.5-pro-001', 'gemini-1.5-flash-001', 'pixtral-large-2411', 'step-1o-vision-32k-highres', 'claude-3-haiku', 'claude-3-sonnet', 'claude-3-opus', 'qwen-vl-max-1119', 'qwen-vl-max-0809', 'reka-core', 'reka-flash', 'aya-vision-32b', 'pixtral-12b-2409', 'microsoft/Phi-4-multimodal-instruct', 'meta-llama/Llama-3.2-90B-Vision-Instruct', 'phi-4-multimodal', 'llama-3.2-90b-vision', 'meta-llama/Llama-3.2-11B-Vision-Instruct', 'Qwen/Qwen2-VL-7B-Instruct', 'llama-3.2-11b-vision', 'qwen-2vl-7b', 'gemini-2.5-pro-preview-05-06', 'o3-2025-04-16', 'o4-mini-2025-04-16', 'mistral-medium-2505', 'gemini-2.5-flash-preview-04-17', 'gpt-4.1-2025-04-14', 'claude-3-7-sonnet-20250219', 'claude-3-7-sonnet-20250219-thinking-32k', 'llama-4-maverick-17b-128e-instruct', 'llama-4-scout-17b-16e-instruct', 'gpt-4.1-mini-2025-04-14', 'gpt-4.1-nano-2025-04-14', 'gemini-2.0-flash-001', 'gemini-2.0-flash-lite-preview-02-05', 'gemma-3-27b-it', 'claude-3-5-sonnet-20241022', 'gpt-4o-mini-2024-07-18', 'gpt-4o-2024-11-20', 'gpt-4o-2024-08-06', 'gpt-4o-2024-05-13', 'mistral-small-3.1-24b-instruct-2503', 'claude-3-5-sonnet-20240620', 'amazon-nova-pro-v1.0', 'amazon-nova-lite-v1.0', 'qwen2.5-vl-32b-instruct', 'qwen2.5-vl-72b-instruct', 'gemini-1.5-pro-002', 'gemini-1.5-flash-002', 'gemini-1.5-flash-8b-001', 'gemini-1.5-pro-001', 'gemini-1.5-flash-001', 'pixtral-large-2411', 'step-1o-vision-32k-highres', 'claude-3-haiku-20240307', 'claude-3-sonnet-20240229', 'claude-3-opus-20240229', 'qwen-vl-max-1119', 'qwen-vl-max-0809', 'reka-core-20240904', 'reka-flash-20240904', 'c4ai-aya-vision-32b', 'pixtral-12b-2409', 'gemini-2.5-pro', 'o3', 'o4-mini', 'mistral-medium-2505', 'gemini-2.5-flash', 'gpt-4.1', 'claude-3-7-sonnet', 'claude-3-7-sonnet-20250219-thinking-32k', 'llama-4-maverick-17b-128e', 'llama-4-scout-17b-16e', 'gpt-4.1-mini', 'gpt-4.1-nano', 'gemini-2.0-flash-001', 'gemini-2.0-flash-lite', 'gemma-3-27b-it', 'claude-3-5-sonnet', 'gpt-4o-mini', 'gpt-4o', 'gpt-4o', 'gpt-4o', 'mistral-small-3.1-24b-instruct-2503', 'claude-3-5-sonnet', 'amazon-nova-pro-v1.0', 'amazon-nova-lite-v1.0', 'qwen2.5-vl-32b', 'qwen2.5-vl-72b', 'gemini-1.5-pro-002', 'gemini-1.5-flash-002', 'gemini-1.5-flash-8b-001', 'gemini-1.5-pro-001', 'gemini-1.5-flash-001', 'pixtral-large-2411', 'step-1o-vision-32k-highres', 'claude-3-haiku', 'claude-3-sonnet', 'claude-3-opus', 'qwen-vl-max-1119', 'qwen-vl-max-0809', 'reka-core', 'reka-flash', 'aya-vision-32b', 'pixtral-12b-2409', 'gpt-4o', 'gpt-4o-mini', 'o1', 'o1-mini', 'o1-pro', 'o3', 'o3-mini', 'o4-mini', 'gpt-4.1', 'gpt-4.1-mini', 'gpt-4.1-nano', 'gpt-4.5-preview', 'grok-vision-beta', 'grok-2-vision', 'gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'gpt-4o-search', 'gpt-4o-mini-search', 'o3-mini-high', 'o4-mini-high', 'gpt-4.5', 'phi-4-multimodal', 'gpt-4o', 'gpt-4o-mini', 'o1', 'o1-mini', 'o1-pro', 'o3', 'o3-mini', 'o4-mini', 'gpt-4.1', 'gpt-4.1-mini', 'gpt-4.1-nano', 'gpt-4.5', 'grok-vision-beta', 'grok-2-vision', 'gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'gpt-4o-search', 'gpt-4o-mini-search', 'o3-mini-high', 'o4-mini-high', 'gpt-4.5', 'phi-4-multimodal', 'janus-pro-7b', 'phi-4-multimodal', 'janus-pro-7b', 'phi-4-multimodal']
video_models = ['Wan-AI/Wan2.1-T2V-14B', 'tencent/HunyuanVideo', 'Wan-AI/Wan2.1-T2V-1.3B', 'THUDM/CogVideoX-5b', 'genmo/mochi-1-preview', 'Lightricks/LTX-Video-0.9.7-distilled', 'wan2.1-t2v-14b', 'hunyuanvideo', 'wan2.1-t2v-1.3b', 'cogvideox-5b', 'mochi-1', 'ltx-video-0.9.7-distilled', 'video']
model_map = {
  "default": {
    "OIVSCodeSer0501": "",
    "OIVSCodeSer2": "",
    "Blackbox": "",
    "Copilot": "",
    "DeepInfraChat": "",
    "OperaAria": "",
    "Startnest": "",
    "LambdaChat": "",
    "PollinationsAI": "",
    "Together": "",
    "Free2GPT": "",
    "Chatai": "",
    "WeWordle": "",
    "OpenaiChat": "",
    "Cloudflare": ""
  },
  "gpt-4": {
    "Blackbox": "gpt-4",
    "PollinationsAI": "openai",
    "Copilot": "gpt-4",
    "Yqcloud": "gpt-4",
    "WeWordle": "gpt-4",
    "OpenaiChat": "gpt-4",
    "Copilot": "Copilot",
    "HarProvider": [
      "gpt-4-1106-preview",
      "gpt-4-0125-preview",
      "gpt-4-0314",
      "gpt-4-0613"
    ],
    "LegacyLMArena": [
      "gpt-4-1106-preview",
      "gpt-4-0125-preview",
      "gpt-4-0314",
      "gpt-4-0613"
    ],
    "PuterJS": [
      "openrouter:openai/gpt-4-1106-preview",
      "openrouter:openai/gpt-4-32k",
      "openrouter:openai/gpt-4-32k-0314",
      "openrouter:openai/gpt-4",
      "openrouter:openai/gpt-4-0314"
    ]
  },
  "gpt-4o": {
    "Blackbox": "gpt-4o",
    "PollinationsAI": "openai",
    "OpenaiChat": "gpt-4o",
    "Copilot": "Copilot",
    "HarProvider": [
      "chatgpt-4o-latest-20250326",
      "chatgpt-4o-latest-20250129",
      "chatgpt-4o-latest-20241120",
      "chatgpt-4o-latest-20240903",
      "chatgpt-4o-latest-20240808",
      "gpt-4o-2024-05-13",
      "gpt-4o-2024-08-06",
      "gpt-4o-2024-11-20"
    ],
    "LegacyLMArena": [
      "chatgpt-4o-latest-20250326",
      "chatgpt-4o-latest-20250129",
      "chatgpt-4o-latest-20241120",
      "chatgpt-4o-latest-20240903",
      "chatgpt-4o-latest-20240808",
      "gpt-4o-2024-05-13",
      "gpt-4o-2024-08-06",
      "gpt-4o-2024-11-20"
    ],
    "PuterJS": [
      "gpt-4o",
      "openrouter:openai/gpt-4o-2024-08-06",
      "openrouter:openai/gpt-4o-2024-11-20",
      "openrouter:openai/chatgpt-4o-latest",
      "openrouter:openai/gpt-4o",
      "openrouter:openai/gpt-4o:extended",
      "openrouter:openai/gpt-4o-2024-05-13"
    ]
  },
  "gpt-4o-mini": {
    "Blackbox": "gpt-4o-mini",
    "PollinationsAI": "openai",
    "Chatai": "gpt-4o-mini-2024-07-18",
    "OIVSCodeSer2": "gpt-4o-mini",
    "Startnest": "gpt-4o-mini",
    "OpenaiChat": "gpt-4o-mini",
    "HarProvider": "gpt-4o-mini-2024-07-18",
    "LegacyLMArena": "gpt-4o-mini-2024-07-18",
    "PuterJS": [
      "gpt-4o-mini",
      "openrouter:openai/gpt-4o-mini",
      "openrouter:openai/gpt-4o-mini-2024-07-18"
    ]
  },
  "gpt-4o-mini-audio": {
    "PollinationsAI": "openai-audio"
  },
  "gpt-4o-mini-tts": {
    "OpenAIFM": "coral"
  },
  "o1": {
    "Copilot": "o1",
    "OpenaiAccount": "o1",
    "OpenaiChat": "o1",
    "CopilotAccount": "Think Deeper",
    "HarProvider": [
      "o1-2024-12-17",
      "o1-preview"
    ],
    "LegacyLMArena": [
      "o1-2024-12-17",
      "o1-preview"
    ],
    "PuterJS": [
      "o1",
      "openrouter:openai/o1",
      "openrouter:openai/o1-preview",
      "openrouter:openai/o1-preview-2024-09-12"
    ]
  },
  "o1-mini": {
    "OpenaiAccount": "o1-mini",
    "OpenaiChat": "o1-mini",
    "HarProvider": "o1-mini",
    "LegacyLMArena": "o1-mini",
    "PuterJS": [
      "o1-mini",
      "openrouter:openai/o1-mini",
      "openrouter:openai/o1-mini-2024-09-12"
    ]
  },
  "o3-mini": {
    "OpenaiChat": "o3-mini",
    "HarProvider": [
      "o3-mini-high",
      "o3-mini"
    ],
    "LegacyLMArena": [
      "o3-mini-high",
      "o3-mini"
    ],
    "LMArenaBeta": "o3-mini",
    "PuterJS": [
      "o3-mini",
      "openrouter:openai/o3-mini",
      "openrouter:openai/o3-mini-high"
    ]
  },
  "o3-mini-high": {
    "OpenaiAccount": "o3-mini-high",
    "OpenaiChat": "o3-mini-high",
    "HarProvider": "o3-mini-high",
    "LegacyLMArena": "o3-mini-high",
    "PuterJS": "openrouter:openai/o3-mini-high"
  },
  "o4-mini": {
    "PollinationsAI": "openai-reasoning",
    "OpenaiChat": "o4-mini",
    "HarProvider": "o4-mini-2025-04-16",
    "LegacyLMArena": "o4-mini-2025-04-16",
    "LMArenaBeta": "o4-mini-2025-04-16",
    "PuterJS": [
      "o4-mini",
      "openrouter:openai/o4-mini"
    ]
  },
  "o4-mini-high": {
    "OpenaiChat": "o4-mini-high",
    "PuterJS": "openrouter:openai/o4-mini-high"
  },
  "gpt-4.1": {
    "PollinationsAI": "openai-large",
    "OpenaiChat": "gpt-4-1",
    "HarProvider": "gpt-4.1-2025-04-14",
    "LegacyLMArena": "gpt-4.1-2025-04-14",
    "LMArenaBeta": "gpt-4.1-2025-04-14",
    "PuterJS": [
      "gpt-4.1",
      "openrouter:openai/gpt-4.1"
    ]
  },
  "gpt-4.1-mini": {
    "Blackbox": "gpt-4.1-mini",
    "OIVSCodeSer0501": "gpt-4.1-mini",
    "PollinationsAI": "openai",
    "OpenaiChat": "gpt-4-1-mini",
    "HarProvider": "gpt-4.1-mini-2025-04-14",
    "LegacyLMArena": "gpt-4.1-mini-2025-04-14",
    "LMArenaBeta": "gpt-4.1-mini-2025-04-14",
    "PuterJS": [
      "gpt-4.1-mini",
      "openrouter:openai/gpt-4.1-mini"
    ]
  },
  "gpt-4.1-nano": {
    "Blackbox": "gpt-4.1-nano",
    "PollinationsAI": "openai-fast",
    "HarProvider": "gpt-4.1-nano-2025-04-14",
    "LegacyLMArena": "gpt-4.1-nano-2025-04-14",
    "PuterJS": [
      "gpt-4.1-nano",
      "openrouter:openai/gpt-4.1-nano"
    ]
  },
  "gpt-4.5": {
    "OpenaiChat": "gpt-4-5",
    "PuterJS": [
      "gpt-4.5-preview",
      "openrouter:openai/gpt-4.5-preview"
    ]
  },
  "dall-e-3": {
    "CopilotAccount": "Copilot",
    "OpenaiAccount": "dall-e-3",
    "MicrosoftDesigner": "dall-e-3",
    "BingCreateImages": "dall-e-3",
    "OpenaiChat": "dall-e-3",
    "LMArenaBeta": "dall-e-3"
  },
  "gpt-image": {
    "PollinationsImage": "gpt-image",
    "PollinationsAI": "gptimage"
  },
  "meta-ai": {
    "MetaAI": "meta-ai"
  },
  "llama-2-7b": {
    "Cloudflare": "@cf/meta/llama-2-7b-chat-int8",
    "HarProvider": "llama-2-7b-chat",
    "LegacyLMArena": "llama-2-7b-chat"
  },
  "llama-2-70b": {
    "Together": "llama-2-70b",
    "HarProvider": [
      "llama-2-70b-chat",
      "llama2-70b-steerlm-chat"
    ],
    "LegacyLMArena": [
      "llama-2-70b-chat",
      "llama2-70b-steerlm-chat"
    ],
    "PuterJS": "openrouter:meta-llama/llama-2-70b-chat"
  },
  "llama-3-8b": {
    "Together": "llama-3-8b",
    "Cloudflare": "@hf/meta-llama/meta-llama-3-8b-instruct",
    "HarProvider": "llama-3-8b-instruct",
    "HuggingFace": "meta-llama/Meta-Llama-3-8B-Instruct",
    "LegacyLMArena": "llama-3-8b-instruct",
    "PuterJS": "openrouter:meta-llama/llama-3-8b-instruct"
  },
  "llama-3-70b": {
    "Together": "llama-3-70b",
    "HarProvider": "llama-3-70b-instruct",
    "LegacyLMArena": "llama-3-70b-instruct",
    "PuterJS": "openrouter:meta-llama/llama-3-70b-instruct"
  },
  "llama-3.1-8b": {
    "DeepInfraChat": "meta-llama/Meta-Llama-3.1-8B-Instruct",
    "Together": "llama-3.1-8b",
    "Cloudflare": "@cf/meta/llama-3.1-8b-instruct-fp8",
    "HarProvider": "llama-3.1-8b-instruct",
    "HuggingFace": "meta-llama/Llama-3.1-8B-Instruct",
    "LegacyLMArena": "llama-3.1-8b-instruct",
    "PuterJS": [
      "openrouter:meta-llama/llama-3.1-8b-instruct:free",
      "openrouter:meta-llama/llama-3.1-8b-instruct"
    ]
  },
  "llama-3.1-70b": {
    "Together": "llama-3.1-70b",
    "HarProvider": "llama-3.1-70b-instruct",
    "LegacyLMArena": "llama-3.1-70b-instruct",
    "PuterJS": "openrouter:meta-llama/llama-3.1-70b-instruct"
  },
  "llama-3.1-405b": {
    "Together": "llama-3.1-405b",
    "HarProvider": [
      "llama-3.1-405b-instruct-bf16",
      "llama-3.1-405b-instruct-fp8"
    ],
    "LegacyLMArena": [
      "llama-3.1-405b-instruct-bf16",
      "llama-3.1-405b-instruct-fp8"
    ],
    "PuterJS": [
      "openrouter:meta-llama/llama-3.1-405b:free",
      "openrouter:meta-llama/llama-3.1-405b",
      "openrouter:meta-llama/llama-3.1-405b-instruct"
    ]
  },
  "llama-3.2-1b": {
    "Cloudflare": "@cf/meta/llama-3.2-1b-instruct",
    "HarProvider": "llama-3.2-1b-instruct",
    "LegacyLMArena": "llama-3.2-1b-instruct",
    "PuterJS": [
      "openrouter:meta-llama/llama-3.2-1b-instruct:free",
      "openrouter:meta-llama/llama-3.2-1b-instruct"
    ]
  },
  "llama-3.2-3b": {
    "Together": "llama-3.2-3b",
    "HarProvider": "llama-3.2-3b-instruct",
    "HuggingFace": "meta-llama/Llama-3.2-3B-Instruct",
    "LegacyLMArena": "llama-3.2-3b-instruct",
    "PuterJS": [
      "openrouter:meta-llama/llama-3.2-3b-instruct:free",
      "openrouter:meta-llama/llama-3.2-3b-instruct"
    ],
    "Cloudflare": "@cf/meta/llama-3.2-3b-instruct"
  },
  "llama-3.2-11b": {
    "Together": "llama-3.2-11b",
    "HuggingFace": "meta-llama/Llama-3.2-11B-Vision-Instruct",
    "PuterJS": [
      "openrouter:meta-llama/llama-3.2-11b-vision-instruct:free",
      "openrouter:meta-llama/llama-3.2-11b-vision-instruct"
    ]
  },
  "llama-3.2-90b": {
    "DeepInfraChat": "meta-llama/Llama-3.2-90B-Vision-Instruct",
    "Together": "llama-3.2-90b",
    "PuterJS": "openrouter:meta-llama/llama-3.2-90b-vision-instruct"
  },
  "llama-3.3-70b": {
    "DeepInfraChat": "meta-llama/Llama-3.3-70B-Instruct",
    "LambdaChat": "llama3.3-70b-instruct-fp8",
    "PollinationsAI": "llama",
    "Together": "llama-3.3-70b",
    "HuggingFace": "meta-llama/Llama-3.3-70B-Instruct",
    "HarProvider": "llama-3.3-70b-instruct",
    "LegacyLMArena": "llama-3.3-70b-instruct",
    "LMArenaBeta": "llama-3.3-70b-instruct",
    "PuterJS": [
      "openrouter:meta-llama/llama-3.3-70b-instruct:free",
      "openrouter:meta-llama/llama-3.3-70b-instruct"
    ],
    "Cloudflare": "@cf/meta/llama-3.3-70b-instruct-fp8-fast"
  },
  "llama-4-scout": {
    "DeepInfraChat": "meta-llama/Llama-4-Scout-17B-16E-Instruct",
    "LambdaChat": "llama-4-scout-17b-16e-instruct",
    "PollinationsAI": "llamascout",
    "Together": "llama-4-scout",
    "Cloudflare": "@cf/meta/llama-4-scout-17b-16e-instruct",
    "HarProvider": "llama-4-scout-17b-16e-instruct",
    "LegacyLMArena": "llama-4-scout-17b-16e-instruct",
    "PuterJS": [
      "openrouter:meta-llama/llama-4-scout:free",
      "openrouter:meta-llama/llama-4-scout"
    ]
  },
  "llama-4-maverick": {
    "DeepInfraChat": "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8",
    "LambdaChat": "llama-4-maverick-17b-128e-instruct-fp8",
    "Together": "llama-4-maverick",
    "HarProvider": [
      "llama-4-maverick-03-26-experimental",
      "llama-4-maverick-17b-128e-instruct"
    ],
    "LegacyLMArena": [
      "llama-4-maverick-03-26-experimental",
      "llama-4-maverick-17b-128e-instruct"
    ],
    "LMArenaBeta": "llama-4-maverick-03-26-experimental",
    "PuterJS": [
      "openrouter:meta-llama/llama-4-maverick:free",
      "openrouter:meta-llama/llama-4-maverick"
    ]
  },
  "mistral-7b": {
    "Together": "mistral-7b",
    "HarProvider": [
      "mistral-7b-instruct-v0.2",
      "mistral-7b-instruct"
    ],
    "LegacyLMArena": [
      "mistral-7b-instruct-v0.2",
      "mistral-7b-instruct"
    ],
    "PuterJS": [
      "open-mistral-7b",
      "openrouter:mistralai/mistral-7b-instruct",
      "openrouter:mistralai/mistral-7b-instruct:free",
      "openrouter:mistralai/mistral-7b-instruct-v0.1",
      "openrouter:mistralai/mistral-7b-instruct-v0.2",
      "openrouter:mistralai/mistral-7b-instruct-v0.3"
    ]
  },
  "mixtral-8x7b": {
    "Together": "mixtral-8x7b",
    "HarProvider": "mixtral-8x7b-instruct-v0.1",
    "LegacyLMArena": "mixtral-8x7b-instruct-v0.1",
    "PuterJS": [
      "open-mixtral-8x7b",
      "openrouter:mistralai/mixtral-8x7b-instruct"
    ]
  },
  "mistral-nemo": {
    "HuggingFace": "mistralai/Mistral-Nemo-Instruct-2407",
    "PuterJS": [
      "openrouter:mistralai/mistral-nemo:free",
      "openrouter:mistralai/mistral-nemo"
    ]
  },
  "mistral-small-24b": {
    "Together": "mistral-small-24b",
    "HarProvider": "mistral-small-24b-instruct-2501",
    "LegacyLMArena": "mistral-small-24b-instruct-2501"
  },
  "mistral-small-3.1-24b": {
    "DeepInfraChat": "mistralai/Mistral-Small-3.1-24B-Instruct-2503",
    "PollinationsAI": "mistral",
    "HarProvider": "mistral-small-3.1-24b-instruct-2503",
    "LegacyLMArena": "mistral-small-3.1-24b-instruct-2503",
    "Cloudflare": "@cf/mistralai/mistral-small-3.1-24b-instruct"
  },
  "hermes-2-dpo": {
    "Together": "hermes-2-dpo",
    "HarProvider": "nous-hermes-2-mixtral-8x7b-dpo",
    "LegacyLMArena": "nous-hermes-2-mixtral-8x7b-dpo",
    "PuterJS": "openrouter:nousresearch/nous-hermes-2-mixtral-8x7b-dpo"
  },
  "phi-4": {
    "DeepInfraChat": "microsoft/phi-4",
    "PollinationsAI": "phi",
    "HuggingSpace": "phi-4-multimodal",
    "HarProvider": "phi-4",
    "LegacyLMArena": "phi-4",
    "PuterJS": "openrouter:microsoft/phi-4"
  },
  "phi-4-multimodal": {
    "DeepInfraChat": "microsoft/Phi-4-multimodal-instruct",
    "HuggingSpace": "phi-4-multimodal",
    "PuterJS": "openrouter:microsoft/phi-4-multimodal-instruct"
  },
  "phi-4-reasoning-plus": {
    "DeepInfraChat": "microsoft/phi-4-reasoning-plus",
    "PuterJS": [
      "openrouter:microsoft/phi-4-reasoning-plus:free",
      "openrouter:microsoft/phi-4-reasoning-plus"
    ]
  },
  "wizardlm-2-7b": {
    "DeepInfraChat": "microsoft/WizardLM-2-7B"
  },
  "wizardlm-2-8x22b": {
    "DeepInfraChat": "microsoft/WizardLM-2-8x22B",
    "PuterJS": "openrouter:microsoft/wizardlm-2-8x22b"
  },
  "gemini-2.0": {
    "Gemini": ""
  },
  "gemini-1.5-flash": {
    "Free2GPT": "gemini-1.5-flash",
    "TeachAnything": "gemini-1.5-flash",
    "HarProvider": [
      "gemini-1.5-flash-002",
      "gemini-1.5-flash-exp-0827",
      "gemini-1.5-flash-001",
      "gemini-1.5-flash-8b-001",
      "gemini-1.5-flash-8b-exp-0827"
    ],
    "LegacyLMArena": [
      "gemini-1.5-flash-002",
      "gemini-1.5-flash-exp-0827",
      "gemini-1.5-flash-001",
      "gemini-1.5-flash-8b-001",
      "gemini-1.5-flash-8b-exp-0827"
    ],
    "PuterJS": [
      "gemini-1.5-flash",
      "openrouter:google/gemini-flash-1.5",
      "gemini-flash-1.5-8b"
    ]
  },
  "gemini-1.5-pro": {
    "Free2GPT": "gemini-1.5-pro",
    "TeachAnything": "gemini-1.5-pro",
    "HarProvider": [
      "gemini-1.5-pro-002",
      "gemini-1.5-pro-exp-0827",
      "gemini-1.5-pro-exp-0801",
      "gemini-1.5-pro-001",
      "gemini-1.5-pro-api-0409-preview"
    ],
    "LegacyLMArena": [
      "gemini-1.5-pro-002",
      "gemini-1.5-pro-exp-0827",
      "gemini-1.5-pro-exp-0801",
      "gemini-1.5-pro-001",
      "gemini-1.5-pro-api-0409-preview"
    ],
    "PuterJS": "openrouter:google/gemini-pro-1.5"
  },
  "gemini-2.0-flash": {
    "Gemini": [
      "gemini-2.0-flash",
      "gemini-2.0-flash",
      "gemini-2.0-flash-exp"
    ],
    "GeminiPro": "gemini-2.0-flash",
    "HarProvider": [
      "gemini-2.0-flash-001",
      "gemini-2.0-flash-exp",
      "gemini-2.0-flash-lite-preview-02-05"
    ],
    "LegacyLMArena": [
      "gemini-2.0-flash-001",
      "gemini-2.0-flash-exp",
      "gemini-2.0-flash-lite-preview-02-05"
    ],
    "PuterJS": [
      "gemini-2.0-flash",
      "openrouter:google/gemini-2.0-flash-lite-001",
      "openrouter:google/gemini-2.0-flash-001",
      "openrouter:google/gemini-2.0-flash-exp:free"
    ]
  },
  "gemini-2.0-flash-thinking": {
    "Gemini": "gemini-2.0-flash-thinking",
    "GeminiPro": "gemini-2.0-flash-thinking",
    "HarProvider": [
      "gemini-2.0-flash-thinking-exp-01-21",
      "gemini-2.0-flash-thinking-exp-1219"
    ],
    "LegacyLMArena": [
      "gemini-2.0-flash-thinking-exp-01-21",
      "gemini-2.0-flash-thinking-exp-1219"
    ]
  },
  "gemini-2.0-flash-thinking-with-apps": {
    "Gemini": "gemini-2.0-flash-thinking-with-apps"
  },
  "gemini-2.5-flash": {
    "Gemini": "gemini-2.5-flash",
    "GeminiPro": "gemini-2.5-flash",
    "HarProvider": "gemini-2.5-flash-preview-04-17",
    "LegacyLMArena": "gemini-2.5-flash-preview-04-17",
    "LMArenaBeta": "gemini-2.5-flash",
    "PuterJS": "openrouter:google/gemini-2.5-flash-preview"
  },
  "gemini-2.5-pro": {
    "Gemini": "gemini-2.5-pro",
    "HarProvider": "gemini-2.5-pro-preview-05-06",
    "LegacyLMArena": "gemini-2.5-pro-preview-05-06",
    "LMArenaBeta": "gemini-2.5-pro",
    "PuterJS": [
      "openrouter:google/gemini-2.5-pro-preview",
      "openrouter:google/gemini-2.5-pro-exp-03-25"
    ]
  },
  "codegemma-7b": {
    "DeepInfraChat": "google/codegemma-7b-it"
  },
  "gemma-2b": {
    "Together": "gemma-2b"
  },
  "gemma-1.1-7b": {
    "DeepInfraChat": "google/gemma-1.1-7b-it"
  },
  "gemma-2-9b": {
    "DeepInfraChat": "google/gemma-2-9b-it",
    "HarProvider": [
      "gemma-2-9b-it-simpo",
      "gemma-2-9b-it"
    ],
    "LegacyLMArena": [
      "gemma-2-9b-it-simpo",
      "gemma-2-9b-it"
    ],
    "PuterJS": [
      "openrouter:google/gemma-2-9b-it:free",
      "openrouter:google/gemma-2-9b-it"
    ]
  },
  "gemma-2-27b": {
    "Together": "gemma-2-27b",
    "HarProvider": "gemma-2-27b-it",
    "DeepInfraChat": "google/gemma-2-27b-it",
    "HuggingFace": "google/gemma-2-27b-it",
    "LegacyLMArena": "gemma-2-27b-it",
    "PuterJS": "openrouter:google/gemma-2-27b-it"
  },
  "gemma-3-4b": {
    "DeepInfraChat": "google/gemma-3-4b-it",
    "HarProvider": "gemma-3-4b-it",
    "LegacyLMArena": "gemma-3-4b-it",
    "PuterJS": [
      "openrouter:google/gemma-3-4b-it:free",
      "openrouter:google/gemma-3-4b-it"
    ]
  },
  "gemma-3-12b": {
    "DeepInfraChat": "google/gemma-3-12b-it",
    "HarProvider": "gemma-3-12b-it",
    "LegacyLMArena": "gemma-3-12b-it",
    "PuterJS": [
      "openrouter:google/gemma-3-12b-it:free",
      "openrouter:google/gemma-3-12b-it"
    ],
    "Cloudflare": "@cf/google/gemma-3-12b-it"
  },
  "gemma-3-27b": {
    "DeepInfraChat": "google/gemma-3-27b-it",
    "Together": "gemma-3-27b",
    "HarProvider": "gemma-3-27b-it",
    "LegacyLMArena": "gemma-3-27b-it",
    "PuterJS": [
      "openrouter:google/gemma-3-27b-it:free",
      "openrouter:google/gemma-3-27b-it"
    ]
  },
  "gemma-3n-e4b": {
    "Together": "gemma-3n-e4b"
  },
  "blackboxai": {
    "Blackbox": "blackboxai"
  },
  "command-r": {
    "HuggingSpace": "command-r-08-2024",
    "HarProvider": [
      "command-r-08-2024",
      "command-r"
    ],
    "LegacyLMArena": [
      "command-r-08-2024",
      "command-r"
    ],
    "PuterJS": [
      "openrouter:cohere/command-r-08-2024",
      "openrouter:cohere/command-r",
      "openrouter:cohere/command-r-03-2024"
    ]
  },
  "command-r-plus": {
    "HuggingSpace": [
      "command-r-plus-08-2024",
      "command-r-plus"
    ],
    "HarProvider": [
      "command-r-plus-08-2024",
      "command-r-plus"
    ],
    "HuggingFace": "CohereForAI/c4ai-command-r-plus-08-2024",
    "LegacyLMArena": [
      "command-r-plus-08-2024",
      "command-r-plus"
    ],
    "PuterJS": [
      "openrouter:cohere/command-r-plus-08-2024",
      "openrouter:cohere/command-r-plus",
      "openrouter:cohere/command-r-plus-04-2024"
    ]
  },
  "command-r7b": {
    "HuggingSpace": [
      "command-r7b-12-2024",
      "command-r7b-arabic-02-2025"
    ],
    "PuterJS": "openrouter:cohere/command-r7b-12-2024"
  },
  "command-a": {
    "HuggingSpace": "command-a-03-2025",
    "HarProvider": "command-a-03-2025",
    "LegacyLMArena": "command-a-03-2025",
    "PuterJS": "openrouter:cohere/command-a"
  },
  "qwen-1.5-7b": {
    "Cloudflare": "@cf/qwen/qwen1.5-7b-chat-awq",
    "HarProvider": "qwen1.5-7b-chat",
    "LegacyLMArena": "qwen1.5-7b-chat"
  },
  "qwen-2-72b": {
    "HuggingSpace": "qwen-qwen2-72b-instruct",
    "Together": "qwen-2-72b",
    "HarProvider": "qwen2-72b-instruct",
    "HuggingFace": "Qwen/Qwen2-72B-Instruct",
    "LegacyLMArena": "qwen2-72b-instruct",
    "PuterJS": "openrouter:qwen/qwen-2-72b-instruct"
  },
  "qwen-2-vl-7b": {
    "HuggingFaceAPI": "qwen-2-vl-7b",
    "HuggingFace": "Qwen/Qwen2-VL-7B-Instruct"
  },
  "qwen-2-vl-72b": {
    "Together": "qwen-2-vl-72b"
  },
  "qwen-2.5": {
    "HuggingSpace": "qwen-qwen2-5"
  },
  "qwen-2.5-7b": {
    "Together": "qwen-2.5-7b",
    "PuterJS": [
      "openrouter:qwen/qwen-2.5-7b-instruct:free",
      "openrouter:qwen/qwen-2.5-7b-instruct"
    ]
  },
  "qwen-2.5-72b": {
    "Together": "qwen-2.5-72b",
    "HarProvider": "qwen2.5-72b-instruct",
    "HuggingFace": "Qwen/Qwen2.5-Coder-32B-Instruct",
    "LegacyLMArena": "qwen2.5-72b-instruct",
    "PuterJS": [
      "openrouter:qwen/qwen-2.5-72b-instruct:free",
      "openrouter:qwen/qwen-2.5-72b-instruct"
    ]
  },
  "qwen-2.5-coder-32b": {
    "PollinationsAI": "qwen-coder",
    "LambdaChat": "qwen25-coder-32b-instruct",
    "Together": "qwen-2.5-coder-32b",
    "HarProvider": "qwen2.5-coder-32b-instruct",
    "HuggingFace": "Qwen/Qwen2.5-Coder-32B-Instruct",
    "LegacyLMArena": "qwen2.5-coder-32b-instruct",
    "PuterJS": [
      "openrouter:qwen/qwen-2.5-coder-32b-instruct:free",
      "openrouter:qwen/qwen-2.5-coder-32b-instruct"
    ],
    "Cloudflare": "@cf/qwen/qwen2.5-coder-32b-instruct"
  },
  "qwen-2.5-1m": {
    "HuggingSpace": "qwen-2.5-1m-demo"
  },
  "qwen-2.5-max": {
    "HuggingSpace": "qwen-qwen2-5-max",
    "HarProvider": "qwen2.5-max",
    "LegacyLMArena": "qwen2.5-max"
  },
  "qwen-2.5-vl-72b": {
    "Together": "qwen-2.5-vl-72b",
    "HarProvider": "qwen2.5-vl-72b-instruct",
    "LegacyLMArena": "qwen2.5-vl-72b-instruct",
    "PuterJS": [
      "openrouter:qwen/qwen2.5-vl-72b-instruct:free",
      "openrouter:qwen/qwen2.5-vl-72b-instruct"
    ]
  },
  "qwen-3-235b": {
    "DeepInfraChat": "Qwen/Qwen3-235B-A22B",
    "Together": "qwen-3-235b",
    "HuggingSpace": "qwen3-235b-a22b",
    "HarProvider": "qwen3-235b-a22b",
    "LegacyLMArena": "qwen3-235b-a22b",
    "PuterJS": [
      "openrouter:qwen/qwen3-235b-a22b:free",
      "openrouter:qwen/qwen3-235b-a22b"
    ]
  },
  "qwen-3-32b": {
    "DeepInfraChat": "Qwen/Qwen3-32B",
    "LambdaChat": "qwen3-32b-fp8",
    "Together": "qwen-3-32b",
    "HuggingSpace": "qwen3-32b",
    "HarProvider": "qwen3-32b",
    "LegacyLMArena": "qwen3-32b",
    "PuterJS": [
      "openrouter:qwen/qwen3-32b:free",
      "openrouter:qwen/qwen3-32b"
    ]
  },
  "qwen-3-30b": {
    "DeepInfraChat": "Qwen/Qwen3-30B-A3B",
    "HuggingSpace": "qwen3-30b-a3b",
    "HarProvider": "qwen3-30b-a3b",
    "LegacyLMArena": "qwen3-30b-a3b",
    "PuterJS": [
      "openrouter:qwen/qwen3-30b-a3b:free",
      "openrouter:qwen/qwen3-30b-a3b"
    ]
  },
  "qwen-3-14b": {
    "DeepInfraChat": "Qwen/Qwen3-14B",
    "HuggingSpace": "qwen3-14b",
    "PuterJS": [
      "openrouter:qwen/qwen3-14b:free",
      "openrouter:qwen/qwen3-14b"
    ]
  },
  "qwen-3-4b": {
    "HuggingSpace": "qwen3-4b",
    "PuterJS": "openrouter:qwen/qwen3-4b:free"
  },
  "qwen-3-1.7b": {
    "HuggingSpace": "qwen3-1.7b",
    "PuterJS": "openrouter:qwen/qwen3-1.7b:free"
  },
  "qwen-3-0.6b": {
    "HuggingSpace": "qwen3-0.6b",
    "PuterJS": "openrouter:qwen/qwen3-0.6b-04-28:free"
  },
  "qwq-32b": {
    "DeepInfraChat": "Qwen/QwQ-32B",
    "Together": "qwq-32b",
    "HarProvider": "qwq-32b-preview",
    "HuggingFace": "Qwen/QwQ-32B",
    "LegacyLMArena": "qwq-32b-preview",
    "LMArenaBeta": "qwq-32b",
    "PuterJS": [
      "openrouter:qwen/qwq-32b-preview",
      "openrouter:qwen/qwq-32b:free",
      "openrouter:qwen/qwq-32b"
    ],
    "Cloudflare": "@cf/qwen/qwq-32b"
  },
  "deepseek-v3": {
    "DeepInfraChat": [
      "deepseek-ai/DeepSeek-V3",
      "deepseek-ai/DeepSeek-V3-0324"
    ],
    "PollinationsAI": "deepseek",
    "Together": "deepseek-v3",
    "HarProvider": [
      "deepseek-v3",
      "deepseek-v3-0324"
    ],
    "LegacyLMArena": [
      "deepseek-v3",
      "deepseek-v3-0324"
    ],
    "PuterJS": "openrouter:deepseek/deepseek-v3-base:free",
    "DeepSeekAPI": "deepseek-v3",
    "LambdaChat": "deepseek-v3"
  },
  "deepseek-r1": {
    "DeepInfraChat": [
      "deepseek-ai/DeepSeek-R1",
      "deepseek-ai/DeepSeek-R1-0528"
    ],
    "LambdaChat": "deepseek-r1",
    "PollinationsAI": "deepseek-reasoning",
    "Together": "deepseek-r1",
    "HuggingFace": "deepseek-ai/DeepSeek-R1",
    "HarProvider": "deepseek-r1",
    "LegacyLMArena": "deepseek-r1",
    "PuterJS": [
      "deepseek-reasoner",
      "openrouter:deepseek/deepseek-r1:free",
      "openrouter:deepseek/deepseek-r1"
    ],
    "DeepSeekAPI": "deepseek-r1"
  },
  "deepseek-r1-turbo": {
    "DeepInfraChat": "deepseek-ai/DeepSeek-R1-Turbo"
  },
  "deepseek-r1-distill-llama-70b": {
    "DeepInfraChat": "deepseek-ai/DeepSeek-R1-Distill-Llama-70B",
    "Together": "deepseek-r1-distill-llama-70b",
    "PuterJS": [
      "openrouter:deepseek/deepseek-r1-distill-llama-70b:free",
      "openrouter:deepseek/deepseek-r1-distill-llama-70b"
    ]
  },
  "deepseek-r1-distill-qwen-1.5b": {
    "Together": "deepseek-r1-distill-qwen-1.5b",
    "PuterJS": "openrouter:deepseek/deepseek-r1-distill-qwen-1.5b"
  },
  "deepseek-r1-distill-qwen-14b": {
    "Together": "deepseek-r1-distill-qwen-14b",
    "PuterJS": [
      "openrouter:deepseek/deepseek-r1-distill-qwen-14b:free",
      "openrouter:deepseek/deepseek-r1-distill-qwen-14b"
    ]
  },
  "deepseek-r1-distill-qwen-32b": {
    "DeepInfraChat": "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B",
    "HuggingFace": "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B",
    "PuterJS": [
      "openrouter:deepseek/deepseek-r1-distill-qwen-32b:free",
      "openrouter:deepseek/deepseek-r1-distill-qwen-32b"
    ]
  },
  "deepseek-prover-v2": {
    "DeepInfraChat": "deepseek-ai/DeepSeek-Prover-V2-671B",
    "PuterJS": [
      "openrouter:deepseek/deepseek-prover-v2:free",
      "openrouter:deepseek/deepseek-prover-v2"
    ]
  },
  "deepseek-prover-v2-671b": {
    "DeepInfraChat": "deepseek-ai/DeepSeek-Prover-V2-671B"
  },
  "deepseek-v3-0324": {
    "DeepInfraChat": "deepseek-ai/DeepSeek-V3-0324",
    "LambdaChat": "deepseek-v3",
    "PollinationsAI": "deepseek",
    "HarProvider": "deepseek-v3-0324",
    "LegacyLMArena": "deepseek-v3-0324",
    "LMArenaBeta": "deepseek-v3-0324",
    "PuterJS": [
      "deepseek-chat",
      "openrouter:deepseek/deepseek-chat-v3-0324:free",
      "openrouter:deepseek/deepseek-chat-v3-0324"
    ]
  },
  "deepseek-v3-0324-turbo": {
    "DeepInfraChat": "deepseek-ai/DeepSeek-V3-0324-Turbo"
  },
  "deepseek-r1-0528": {
    "DeepInfraChat": "deepseek-ai/DeepSeek-R1-0528",
    "LambdaChat": "deepseek-r1-0528",
    "HuggingFace": "deepseek-ai/DeepSeek-R1-0528",
    "LMArenaBeta": "deepseek-r1-0528"
  },
  "deepseek-r1-0528-turbo": {
    "DeepInfraChat": "deepseek-ai/DeepSeek-R1-0528-Turbo"
  },
  "janus-pro-7b": {
    "DeepseekAI_JanusPro7b": "janus-pro-7b",
    "HuggingSpace": "janus-pro-7b"
  },
  "grok-2": {
    "Grok": "grok-2",
    "HarProvider": "grok-2-2024-08-13",
    "LegacyLMArena": "grok-2-2024-08-13",
    "PuterJS": [
      "openrouter:x-ai/grok-2-vision-1212",
      "openrouter:x-ai/grok-2-1212"
    ]
  },
  "grok-3": {
    "Grok": "grok-3",
    "HarProvider": [
      "early-grok-3",
      "grok-3-preview-02-24"
    ],
    "LegacyLMArena": [
      "early-grok-3",
      "grok-3-preview-02-24"
    ],
    "LMArenaBeta": "grok-3-preview-02-24",
    "PuterJS": "grok-3"
  },
  "grok-3-mini": {
    "PollinationsAI": "grok",
    "PuterJS": "openrouter:x-ai/grok-3-mini-beta"
  },
  "grok-3-r1": {
    "Grok": "grok-3-thinking"
  },
  "sonar": {
    "PerplexityLabs": "sonar",
    "PuterJS": "openrouter:perplexity/sonar"
  },
  "sonar-pro": {
    "PerplexityLabs": "sonar-pro",
    "PuterJS": "openrouter:perplexity/sonar-pro"
  },
  "sonar-reasoning": {
    "PerplexityLabs": "sonar-reasoning",
    "PuterJS": "openrouter:perplexity/sonar-reasoning"
  },
  "sonar-reasoning-pro": {
    "PerplexityLabs": "sonar-reasoning-pro",
    "PuterJS": "openrouter:perplexity/sonar-reasoning-pro"
  },
  "r1-1776": {
    "Together": "r1-1776",
    "PerplexityLabs": "r1-1776",
    "PuterJS": "openrouter:perplexity/r1-1776"
  },
  "nemotron-70b": {
    "LambdaChat": "llama3.1-nemotron-70b-instruct",
    "Together": "nemotron-70b",
    "HuggingFace": "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF",
    "HarProvider": "llama-3.1-nemotron-70b-instruct",
    "LegacyLMArena": "llama-3.1-nemotron-70b-instruct",
    "PuterJS": "openrouter:nvidia/llama-3.1-nemotron-70b-instruct"
  },
  "dolphin-2.6": {
    "DeepInfraChat": "cognitivecomputations/dolphin-2.6-mixtral-8x7b"
  },
  "dolphin-2.9": {
    "DeepInfraChat": "cognitivecomputations/dolphin-2.9.1-llama-3-70b"
  },
  "airoboros-70b": {
    "DeepInfraChat": "deepinfra/airoboros-70b"
  },
  "lzlv-70b": {
    "DeepInfraChat": "lizpreciatior/lzlv_70b_fp16_hf"
  },
  "lfm-40b": {
    "LambdaChat": "lfm-40b",
    "PuterJS": "openrouter:liquid/lfm-40b"
  },
  "aria": {
    "OperaAria": "aria"
  },
  "evil": {
    "PollinationsAI": "evil"
  },
  "sdxl-turbo": {
    "HuggingFaceMedia": "stabilityai/sdxl-turbo",
    "PollinationsImage": "sdxl-turbo",
    "ImageLabs": "sdxl-turbo",
    "PollinationsAI": "turbo",
    "HuggingFace": "stabilityai/sdxl-turbo"
  },
  "sd-3.5-large": {
    "HuggingFaceMedia": "stabilityai/stable-diffusion-3.5-large",
    "HuggingSpace": "stabilityai-stable-diffusion-3-5-large",
    "HuggingFace": "stabilityai/stable-diffusion-3.5-large",
    "GeminiPro": "gemma-7b"
  },
  "flux": {
    "HuggingFaceMedia": "black-forest-labs/FLUX.1-dev",
    "PollinationsImage": "flux",
    "Together": "flux",
    "HuggingSpace": "black-forest-labs-flux-1-dev",
    "PollinationsAI": "flux",
    "HuggingFace": "black-forest-labs/FLUX.1-dev"
  },
  "flux-pro": {
    "PollinationsImage": "flux-pro",
    "Together": "flux-pro",
    "PollinationsAI": "flux"
  },
  "flux-dev": {
    "PollinationsImage": "flux-dev",
    "HuggingSpace": "black-forest-labs-flux-1-dev",
    "Together": "flux-dev",
    "HuggingFace": "black-forest-labs/FLUX.1-dev",
    "PollinationsAI": "flux",
    "HuggingFaceMedia": "black-forest-labs/FLUX.1-dev"
  },
  "flux-schnell": {
    "PollinationsImage": "flux-schnell",
    "Together": "flux-schnell",
    "HuggingFace": "black-forest-labs/FLUX.1-schnell",
    "PollinationsAI": "flux",
    "HuggingFaceMedia": "black-forest-labs/FLUX.1-schnell"
  },
  "flux-redux": {
    "Together": "flux-redux"
  },
  "flux-depth": {
    "Together": "flux-depth"
  },
  "flux-canny": {
    "Together": "flux-canny"
  },
  "flux-kontext-max": {
    "Together": "flux-kontext-max"
  },
  "flux-dev-lora": {
    "Together": "flux-dev-lora"
  },
  "flux-kontext-pro": {
    "Together": "flux-kontext-pro"
  },
  "flux-kontext-dev": {
    "Together": "flux-kontext-dev",
    "HuggingSpace": "flux-kontext-dev"
  },
  "auto": {
    "OpenaiChat": "auto"
  },
  "PollinationsAI:openai": {
    "PollinationsAI": "openai"
  },
  "PollinationsAI:evil": {
    "PollinationsAI": "evil"
  },
  "PollinationsAI:deepseek-v3": {
    "PollinationsAI": "deepseek-v3"
  },
  "PollinationsAI:deepseek-r1": {
    "PollinationsAI": "deepseek-r1"
  },
  "PollinationsAI:gemma-roblox": {
    "PollinationsAI": "gemma-roblox"
  },
  "PollinationsAI:grok-3-mini-high": {
    "PollinationsAI": "grok-3-mini-high"
  },
  "PollinationsAI:llama-fast-roblox": {
    "PollinationsAI": "llama-fast-roblox"
  },
  "PollinationsAI:llama-roblox": {
    "PollinationsAI": "llama-roblox"
  },
  "PollinationsAI:llama-4-scout": {
    "PollinationsAI": "llama-4-scout"
  },
  "PollinationsAI:mistral-small-3.1-24b": {
    "PollinationsAI": "mistral-small-3.1-24b"
  },
  "PollinationsAI:mistral-nemo-roblox": {
    "PollinationsAI": "mistral-nemo-roblox"
  },
  "PollinationsAI:mistral-roblox": {
    "PollinationsAI": "mistral-roblox"
  },
  "PollinationsAI:gpt-4o-mini": {
    "PollinationsAI": "gpt-4o-mini"
  },
  "PollinationsAI:gpt-4o-audio": {
    "PollinationsAI": "gpt-4o-audio"
  },
  "PollinationsAI:gpt-4.1-nano": {
    "PollinationsAI": "gpt-4.1-nano"
  },
  "PollinationsAI:gpt-4.1": {
    "PollinationsAI": "gpt-4.1"
  },
  "PollinationsAI:o4-mini": {
    "PollinationsAI": "o4-mini"
  },
  "PollinationsAI:openai-roblox": {
    "PollinationsAI": "openai-roblox"
  },
  "PollinationsAI:phi-4": {
    "PollinationsAI": "phi-4"
  },
  "PollinationsAI:qwen-2.5-coder-32b": {
    "PollinationsAI": "qwen-2.5-coder-32b"
  },
  "PollinationsAI:bidara": {
    "PollinationsAI": "bidara"
  },
  "PollinationsAI:elixposearch": {
    "PollinationsAI": "elixposearch"
  },
  "PollinationsAI:hypnosis-tracy": {
    "PollinationsAI": "hypnosis-tracy"
  },
  "PollinationsAI:midijourney": {
    "PollinationsAI": "midijourney"
  },
  "PollinationsAI:mirexa": {
    "PollinationsAI": "mirexa"
  },
  "PollinationsAI:rtist": {
    "PollinationsAI": "rtist"
  },
  "PollinationsAI:sur": {
    "PollinationsAI": "sur"
  },
  "PollinationsAI:unity": {
    "PollinationsAI": "unity"
  },
  "PollinationsAI:flux": {
    "PollinationsAI": "flux"
  },
  "PollinationsAI:turbo": {
    "PollinationsAI": "turbo"
  },
  "PollinationsAI:kontext": {
    "PollinationsAI": "kontext"
  },
  "PollinationsAI:gptimage": {
    "PollinationsAI": "gptimage"
  },
  "PollinationsAI:transparent": {
    "PollinationsAI": "transparent"
  },
  "PollinationsAI:openai-audio": {
    "PollinationsAI": "openai-audio"
  },
  "PollinationsAI:gpt-4o-mini-audio": {
    "PollinationsAI": "gpt-4o-mini-audio"
  },
  "PollinationsAI:alloy": {
    "PollinationsAI": "alloy"
  },
  "PollinationsAI:echo": {
    "PollinationsAI": "echo"
  },
  "PollinationsAI:fable": {
    "PollinationsAI": "fable"
  },
  "PollinationsAI:onyx": {
    "PollinationsAI": "onyx"
  },
  "PollinationsAI:nova": {
    "PollinationsAI": "nova"
  },
  "PollinationsAI:shimmer": {
    "PollinationsAI": "shimmer"
  },
  "PollinationsAI:coral": {
    "PollinationsAI": "coral"
  },
  "PollinationsAI:verse": {
    "PollinationsAI": "verse"
  },
  "PollinationsAI:ballad": {
    "PollinationsAI": "ballad"
  },
  "PollinationsAI:ash": {
    "PollinationsAI": "ash"
  },
  "PollinationsAI:sage": {
    "PollinationsAI": "sage"
  },
  "PollinationsAI:amuch": {
    "PollinationsAI": "amuch"
  },
  "PollinationsAI:dan": {
    "PollinationsAI": "dan"
  },
  "grok-3-mini-high": {
    "PollinationsAI": "grok",
    "LMArenaBeta": "grok-3-mini-high"
  },
  "gpt-4o-audio": {
    "PollinationsAI": "openai-audio"
  },
  "": {
    "Gemini": ""
  },
  "grok-3-thinking": {
    "Grok": "grok-3-thinking"
  },
  "chatgpt-4o-latest": {
    "HarProvider": "chatgpt-4o-latest-20250326",
    "LegacyLMArena": "chatgpt-4o-latest-20250326",
    "LMArenaBeta": "chatgpt-4o-latest-20250326"
  },
  "gemma-2b-it": {
    "HarProvider": "gemma-2b-it",
    "LegacyLMArena": "gemma-2b-it"
  },
  "gemma-3-4b-it": {
    "HarProvider": "gemma-3-4b-it",
    "DeepInfraChat": "google/gemma-3-4b-it",
    "LegacyLMArena": "gemma-3-4b-it",
    "GeminiPro": "gemma-3-4b-it"
  },
  "chatglm3-6b": {
    "HarProvider": "chatglm3-6b",
    "LegacyLMArena": "chatglm3-6b"
  },
  "gemini-1.5-pro-exp-0827": {
    "HarProvider": "gemini-1.5-pro-exp-0827",
    "LegacyLMArena": "gemini-1.5-pro-exp-0827"
  },
  "mistral-medium-2505": {
    "HarProvider": "mistral-medium-2505",
    "LegacyLMArena": "mistral-medium-2505",
    "LMArenaBeta": "mistral-medium-2505",
    "PuterJS": "mistral-medium-2505"
  },
  "starling-lm-7b-beta": {
    "HarProvider": "starling-lm-7b-beta",
    "LegacyLMArena": "starling-lm-7b-beta",
    "Cloudflare": "@hf/nexusflow/starling-lm-7b-beta"
  },
  "gemini-1.5-pro-exp-0801": {
    "HarProvider": "gemini-1.5-pro-exp-0801",
    "LegacyLMArena": "gemini-1.5-pro-exp-0801"
  },
  "nous-hermes-2-mixtral-8x7b-dpo": {
    "HarProvider": "nous-hermes-2-mixtral-8x7b-dpo",
    "LegacyLMArena": "nous-hermes-2-mixtral-8x7b-dpo"
  },
  "zephyr-7b-beta": {
    "HarProvider": "zephyr-7b-beta",
    "LegacyLMArena": "zephyr-7b-beta",
    "Cloudflare": "@hf/thebloke/zephyr-7b-beta-awq"
  },
  "phi-3-medium-4k": {
    "HarProvider": "phi-3-medium-4k-instruct",
    "LegacyLMArena": "phi-3-medium-4k-instruct"
  },
  "yi-1.5-34b": {
    "HarProvider": "yi-1.5-34b-chat",
    "LegacyLMArena": "yi-1.5-34b-chat"
  },
  "llama-4-scout-17b-16e": {
    "HarProvider": "llama-4-scout-17b-16e-instruct",
    "LambdaChat": "llama-4-scout-17b-16e-instruct",
    "DeepInfraChat": "meta-llama/Llama-4-Scout-17B-16E-Instruct",
    "LegacyLMArena": "llama-4-scout-17b-16e-instruct",
    "LMArenaBeta": "llama-4-scout-17b-16e-instruct"
  },
  "gpt-4-0125": {
    "HarProvider": "gpt-4-0125-preview",
    "LegacyLMArena": "gpt-4-0125-preview"
  },
  "early-grok-3": {
    "HarProvider": "early-grok-3",
    "LegacyLMArena": "early-grok-3"
  },
  "reka-core": {
    "HarProvider": [
      "reka-core-20240904",
      "reka-core-20240722",
      "reka-core-20240501"
    ],
    "LegacyLMArena": [
      "reka-core-20240904",
      "reka-core-20240722",
      "reka-core-20240501"
    ]
  },
  "qwen-3235b-a22b": {
    "HarProvider": "qwen3-235b-a22b",
    "DeepInfraChat": "Qwen/Qwen3-235B-A22B",
    "HuggingFace": "Qwen/Qwen3-235B-A22B",
    "LegacyLMArena": "qwen3-235b-a22b",
    "LMArenaBeta": "qwen3-235b-a22b"
  },
  "gemini-1.5-flash-002": {
    "HarProvider": "gemini-1.5-flash-002",
    "LegacyLMArena": "gemini-1.5-flash-002"
  },
  "mpt-30b": {
    "HarProvider": "mpt-30b-chat",
    "LegacyLMArena": "mpt-30b-chat"
  },
  "deepseek": {
    "HarProvider": "deepseek-v3",
    "LambdaChat": "deepseek-v3",
    "DeepInfraChat": "deepseek-ai/DeepSeek-V3",
    "HuggingFace": "deepseek-ai/DeepSeek-V3",
    "LegacyLMArena": "deepseek-v3",
    "PuterJS": "deepseek-chat"
  },
  "gemini-1.5-pro-api-0409": {
    "HarProvider": "gemini-1.5-pro-api-0409-preview",
    "LegacyLMArena": "gemini-1.5-pro-api-0409-preview"
  },
  "gemini-2.0-flash-thinking-exp": {
    "HarProvider": "gemini-2.0-flash-thinking-exp-01-21",
    "LegacyLMArena": "gemini-2.0-flash-thinking-exp-01-21",
    "GeminiPro": "gemini-2.0-flash-thinking-exp"
  },
  "vicuna-13b": {
    "HarProvider": "vicuna-13b",
    "LegacyLMArena": "vicuna-13b"
  },
  "gemini-1.5-flash-8b-exp-0827": {
    "HarProvider": "gemini-1.5-flash-8b-exp-0827",
    "LegacyLMArena": "gemini-1.5-flash-8b-exp-0827"
  },
  "granite-3.1-2b": {
    "HarProvider": "granite-3.1-2b-instruct",
    "LegacyLMArena": "granite-3.1-2b-instruct"
  },
  "claude-instant-1": {
    "HarProvider": "claude-instant-1",
    "LegacyLMArena": "claude-instant-1"
  },
  "mistral-large-2407": {
    "HarProvider": "mistral-large-2407",
    "LegacyLMArena": "mistral-large-2407"
  },
  "claude-3-opus": {
    "HarProvider": "claude-3-opus-20240229",
    "LegacyLMArena": "claude-3-opus-20240229",
    "PuterJS": [
      "openrouter:anthropic/claude-3-opus:beta",
      "openrouter:anthropic/claude-3-opus"
    ]
  },
  "gemini-1.5-pro-001": {
    "HarProvider": "gemini-1.5-pro-001",
    "LegacyLMArena": "gemini-1.5-pro-001"
  },
  "claude-3-5-haiku": {
    "HarProvider": "claude-3-5-haiku-20241022",
    "LegacyLMArena": "claude-3-5-haiku-20241022",
    "LMArenaBeta": "claude-3-5-haiku-20241022"
  },
  "o3": {
    "HarProvider": "o3-2025-04-16",
    "LegacyLMArena": "o3-2025-04-16",
    "LMArenaBeta": "o3-2025-04-16",
    "PuterJS": [
      "o3",
      "openrouter:openai/o3"
    ]
  },
  "ministral-8b-2410": {
    "HarProvider": "ministral-8b-2410",
    "LegacyLMArena": "ministral-8b-2410",
    "PuterJS": "ministral-8b-2410"
  },
  "gemini-2.0-flash-001": {
    "HarProvider": "gemini-2.0-flash-001",
    "LegacyLMArena": "gemini-2.0-flash-001",
    "LMArenaBeta": "gemini-2.0-flash-001"
  },
  "mistral-7b-instruct-v0.2": {
    "HarProvider": "mistral-7b-instruct-v0.2",
    "HuggingFace": "mistralai/Mistral-7B-Instruct-v0.2",
    "LegacyLMArena": "mistral-7b-instruct-v0.2"
  },
  "qwen1.5-7b": {
    "HarProvider": "qwen1.5-7b-chat",
    "LegacyLMArena": "qwen1.5-7b-chat"
  },
  "deepseek-v2-api-0628": {
    "HarProvider": "deepseek-v2-api-0628",
    "LegacyLMArena": "deepseek-v2-api-0628"
  },
  "palm-2": {
    "HarProvider": "palm-2",
    "LegacyLMArena": "palm-2"
  },
  "step-2-16k-exp-202412": {
    "HarProvider": "step-2-16k-exp-202412",
    "LegacyLMArena": "step-2-16k-exp-202412"
  },
  "qwen1.5-32b": {
    "HarProvider": "qwen1.5-32b-chat",
    "LegacyLMArena": "qwen1.5-32b-chat"
  },
  "gemini-1.5-flash-8b-001": {
    "HarProvider": "gemini-1.5-flash-8b-001",
    "LegacyLMArena": "gemini-1.5-flash-8b-001"
  },
  "mistral-next": {
    "HarProvider": "mistral-next",
    "LegacyLMArena": "mistral-next"
  },
  "chatglm-6b": {
    "HarProvider": "chatglm-6b",
    "LegacyLMArena": "chatglm-6b"
  },
  "internlm2.5-20b": {
    "HarProvider": "internlm2_5-20b-chat",
    "LegacyLMArena": "internlm2_5-20b-chat"
  },
  "qwen-272b": {
    "HarProvider": "qwen2-72b-instruct",
    "LegacyLMArena": "qwen2-72b-instruct"
  },
  "qwen2.5-coder-32b": {
    "HarProvider": "qwen2.5-coder-32b-instruct",
    "HuggingFace": "Qwen/Qwen2.5-Coder-32B-Instruct",
    "LegacyLMArena": "qwen2.5-coder-32b-instruct"
  },
  "gemini-1.5-flash-exp-0827": {
    "HarProvider": "gemini-1.5-flash-exp-0827",
    "LegacyLMArena": "gemini-1.5-flash-exp-0827"
  },
  "dolphin-2.2.1-mistral-7b": {
    "HarProvider": "dolphin-2.2.1-mistral-7b",
    "LegacyLMArena": "dolphin-2.2.1-mistral-7b"
  },
  "qwen1.5-110b": {
    "HarProvider": "qwen1.5-110b-chat",
    "LegacyLMArena": "qwen1.5-110b-chat"
  },
  "dbrx-instruct": {
    "HarProvider": "dbrx-instruct-preview",
    "LegacyLMArena": "dbrx-instruct-preview"
  },
  "llama-3.1-nemotron-70b": {
    "HarProvider": "llama-3.1-nemotron-70b-instruct",
    "LambdaChat": "llama3.1-nemotron-70b-instruct",
    "LegacyLMArena": "llama-3.1-nemotron-70b-instruct"
  },
  "aya-expanse-8b": {
    "HarProvider": "c4ai-aya-expanse-8b",
    "LegacyLMArena": "c4ai-aya-expanse-8b"
  },
  "claude-3-7-sonnet": {
    "HarProvider": "claude-3-7-sonnet-20250219",
    "LegacyLMArena": "claude-3-7-sonnet-20250219",
    "LMArenaBeta": "claude-3-7-sonnet-20250219",
    "PuterJS": "claude-3-7-sonnet-20250219"
  },
  "gemini-2.0-flash-exp": {
    "HarProvider": "gemini-2.0-flash-exp",
    "LegacyLMArena": "gemini-2.0-flash-exp"
  },
  "llama-3.1-405b-instruct": {
    "HarProvider": "llama-3.1-405b-instruct-bf16",
    "LegacyLMArena": "llama-3.1-405b-instruct-bf16"
  },
  "claude-3-7-sonnet-20250219-thinking-32k": {
    "HarProvider": "claude-3-7-sonnet-20250219-thinking-32k",
    "LegacyLMArena": "claude-3-7-sonnet-20250219-thinking-32k",
    "LMArenaBeta": "claude-3-7-sonnet-20250219-thinking-32k"
  },
  "reka-flash": {
    "HarProvider": [
      "reka-flash-20240904",
      "reka-flash-20240722",
      "reka-flash-preview-20240611",
      "reka-flash-21b-********-online",
      "reka-flash-21b-********"
    ],
    "LegacyLMArena": [
      "reka-flash-20240904",
      "reka-flash-20240722",
      "reka-flash-preview-20240611",
      "reka-flash-21b-********-online",
      "reka-flash-21b-********"
    ],
    "PuterJS": "openrouter:rekaai/reka-flash-3:free"
  },
  "amazon-nova-pro-v1.0": {
    "HarProvider": "amazon-nova-pro-v1.0",
    "LegacyLMArena": "amazon-nova-pro-v1.0"
  },
  "gpt-4-0314": {
    "HarProvider": "gpt-4-0314",
    "LegacyLMArena": "gpt-4-0314"
  },
  "dolly-v2-12b": {
    "HarProvider": "dolly-v2-12b",
    "LegacyLMArena": "dolly-v2-12b"
  },
  "guanaco-33b": {
    "HarProvider": "guanaco-33b",
    "LegacyLMArena": "guanaco-33b"
  },
  "olmo-7b": {
    "HarProvider": "olmo-7b-instruct",
    "LegacyLMArena": "olmo-7b-instruct"
  },
  "tulu-2-dpo-70b": {
    "HarProvider": "tulu-2-dpo-70b",
    "LegacyLMArena": "tulu-2-dpo-70b"
  },
  "gemini-1.5-flash-001": {
    "HarProvider": "gemini-1.5-flash-001",
    "LegacyLMArena": "gemini-1.5-flash-001"
  },
  "athene-70b-0725": {
    "HarProvider": "athene-70b-0725",
    "LegacyLMArena": "athene-70b-0725"
  },
  "qwen1.5-14b": {
    "HarProvider": "qwen1.5-14b-chat",
    "LegacyLMArena": "qwen1.5-14b-chat"
  },
  "gemma-1.1-2b-it": {
    "HarProvider": "gemma-1.1-2b-it",
    "LegacyLMArena": "gemma-1.1-2b-it"
  },
  "gemini-exp-1121": {
    "HarProvider": "gemini-exp-1121",
    "LegacyLMArena": "gemini-exp-1121"
  },
  "qwen2.5-plus-1127": {
    "HarProvider": "qwen2.5-plus-1127",
    "LegacyLMArena": "qwen2.5-plus-1127"
  },
  "claude-3-haiku": {
    "HarProvider": "claude-3-haiku-20240307",
    "LegacyLMArena": "claude-3-haiku-20240307",
    "PuterJS": [
      "claude-3-haiku-20240307",
      "openrouter:anthropic/claude-3-haiku:beta",
      "openrouter:anthropic/claude-3-haiku"
    ]
  },
  "phi-3-mini-128k": {
    "HarProvider": "phi-3-mini-128k-instruct",
    "LegacyLMArena": "phi-3-mini-128k-instruct"
  },
  "claude-1": {
    "HarProvider": "claude-1",
    "LegacyLMArena": "claude-1"
  },
  "llama-2-13b": {
    "HarProvider": "llama-2-13b-chat",
    "LegacyLMArena": "llama-2-13b-chat",
    "Cloudflare": "@hf/thebloke/llama-2-13b-chat-awq"
  },
  "gpt4all-13b-snoozy": {
    "HarProvider": "gpt4all-13b-snoozy",
    "LegacyLMArena": "gpt4all-13b-snoozy"
  },
  "gemini-pro": {
    "HarProvider": "gemini-pro",
    "LegacyLMArena": "gemini-pro"
  },
  "qwen1.5-4b": {
    "HarProvider": "qwen1.5-4b-chat",
    "LegacyLMArena": "qwen1.5-4b-chat"
  },
  "starling-lm-7b-alpha": {
    "HarProvider": "starling-lm-7b-alpha",
    "LegacyLMArena": "starling-lm-7b-alpha"
  },
  "gpt-3.5-turbo-0125": {
    "HarProvider": "gpt-3.5-turbo-0125",
    "LegacyLMArena": "gpt-3.5-turbo-0125"
  },
  "vicuna-7b": {
    "HarProvider": "vicuna-7b",
    "LegacyLMArena": "vicuna-7b"
  },
  "llama-3.1-nemotron-51b": {
    "HarProvider": "llama-3.1-nemotron-51b-instruct",
    "LegacyLMArena": "llama-3.1-nemotron-51b-instruct"
  },
  "claude-3-sonnet": {
    "HarProvider": "claude-3-sonnet-20240229",
    "LegacyLMArena": "claude-3-sonnet-20240229",
    "PuterJS": [
      "openrouter:anthropic/claude-3-sonnet:beta",
      "openrouter:anthropic/claude-3-sonnet"
    ]
  },
  "smollm2-1.7b": {
    "HarProvider": "smollm2-1.7b-instruct",
    "LegacyLMArena": "smollm2-1.7b-instruct"
  },
  "mistral-medium": {
    "HarProvider": [
      "mistral-medium",
      "mistral-medium-2505"
    ],
    "LegacyLMArena": [
      "mistral-medium",
      "mistral-medium-2505"
    ],
    "PuterJS": [
      "openrouter:mistralai/mistral-medium",
      "openrouter:mistralai/mistral-medium-3"
    ]
  },
  "amazon-nova-micro-v1.0": {
    "HarProvider": "amazon-nova-micro-v1.0",
    "LegacyLMArena": "amazon-nova-micro-v1.0"
  },
  "command-r-plus24": {
    "HarProvider": "command-r-plus-08-2024",
    "HuggingFace": "CohereForAI/c4ai-command-r-plus-08-2024",
    "LegacyLMArena": "command-r-plus-08-2024",
    "HuggingSpace": "command-r-plus-08-2024"
  },
  "jamba-1.5-mini": {
    "HarProvider": "jamba-1.5-mini",
    "LegacyLMArena": "jamba-1.5-mini"
  },
  "bard-jan-24-gemini-pro": {
    "HarProvider": "bard-jan-24-gemini-pro",
    "LegacyLMArena": "bard-jan-24-gemini-pro"
  },
  "pplx-70b-online": {
    "HarProvider": "pplx-70b-online",
    "LegacyLMArena": "pplx-70b-online"
  },
  "stripedhyena-nous-7b": {
    "HarProvider": "stripedhyena-nous-7b",
    "LegacyLMArena": "stripedhyena-nous-7b"
  },
  "qwen2.5-max": {
    "HarProvider": "qwen2.5-max",
    "LegacyLMArena": "qwen2.5-max"
  },
  "chatglm2-6b": {
    "HarProvider": "chatglm2-6b",
    "LegacyLMArena": "chatglm2-6b"
  },
  "gemma-3-27b-it": {
    "HarProvider": "gemma-3-27b-it",
    "DeepInfraChat": "google/gemma-3-27b-it",
    "LegacyLMArena": "gemma-3-27b-it",
    "LMArenaBeta": "gemma-3-27b-it",
    "GeminiPro": "gemma-3-27b-it"
  },
  "qwen-plus-0125": {
    "HarProvider": "qwen-plus-0125",
    "LegacyLMArena": "qwen-plus-0125"
  },
  "gemini-2.0-flash-lite": {
    "HarProvider": "gemini-2.0-flash-lite-preview-02-05",
    "LegacyLMArena": "gemini-2.0-flash-lite-preview-02-05",
    "GeminiPro": "gemini-2.0-flash-lite"
  },
  "yi-lightning": {
    "HarProvider": "yi-lightning",
    "LegacyLMArena": "yi-lightning"
  },
  "codellama-70b": {
    "HarProvider": "codellama-70b-instruct",
    "LegacyLMArena": "codellama-70b-instruct"
  },
  "gpt-4-turbo": {
    "HarProvider": "gpt-4-turbo-2024-04-09",
    "LegacyLMArena": "gpt-4-turbo-2024-04-09",
    "PuterJS": [
      "openrouter:openai/gpt-4-turbo",
      "openrouter:openai/gpt-4-turbo-preview"
    ]
  },
  "glm-4-0116": {
    "HarProvider": "glm-4-0116",
    "LegacyLMArena": "glm-4-0116"
  },
  "gpt-3.5-turbo-0613": {
    "HarProvider": "gpt-3.5-turbo-0613",
    "LegacyLMArena": "gpt-3.5-turbo-0613"
  },
  "vicuna-33b": {
    "HarProvider": "vicuna-33b",
    "LegacyLMArena": "vicuna-33b"
  },
  "jamba-1.5-large": {
    "HarProvider": "jamba-1.5-large",
    "LegacyLMArena": "jamba-1.5-large"
  },
  "gemma-2-9b-it-simpo": {
    "HarProvider": "gemma-2-9b-it-simpo",
    "LegacyLMArena": "gemma-2-9b-it-simpo"
  },
  "gemini-1.5-pro-002": {
    "HarProvider": "gemini-1.5-pro-002",
    "LegacyLMArena": "gemini-1.5-pro-002"
  },
  "gpt-3.5-turbo-0314": {
    "HarProvider": "gpt-3.5-turbo-0314",
    "LegacyLMArena": "gpt-3.5-turbo-0314"
  },
  "openhermes-2.5-mistral-7b": {
    "HarProvider": "openhermes-2.5-mistral-7b",
    "LegacyLMArena": "openhermes-2.5-mistral-7b",
    "Cloudflare": "@hf/thebloke/openhermes-2.5-mistral-7b-awq"
  },
  "gemini-advanced-0514": {
    "HarProvider": "gemini-advanced-0514",
    "LegacyLMArena": "gemini-advanced-0514"
  },
  "granite-3.0-8b": {
    "HarProvider": "granite-3.0-8b-instruct",
    "LegacyLMArena": "granite-3.0-8b-instruct"
  },
  "mixtral-8x7b-instruct-v0.1": {
    "HarProvider": "mixtral-8x7b-instruct-v0.1",
    "LegacyLMArena": "mixtral-8x7b-instruct-v0.1"
  },
  "pplx-7b-online": {
    "HarProvider": "pplx-7b-online",
    "LegacyLMArena": "pplx-7b-online"
  },
  "athene-v2": {
    "HarProvider": "athene-v2-chat",
    "LegacyLMArena": "athene-v2-chat"
  },
  "mixtral-8x22b-instruct-v0.1": {
    "HarProvider": "mixtral-8x22b-instruct-v0.1",
    "LegacyLMArena": "mixtral-8x22b-instruct-v0.1"
  },
  "rwkv-4-raven-14b": {
    "HarProvider": "RWKV-4-Raven-14B",
    "LegacyLMArena": "RWKV-4-Raven-14B"
  },
  "gemini-2.0-flash-thinking-exp-1219": {
    "HarProvider": "gemini-2.0-flash-thinking-exp-1219",
    "LegacyLMArena": "gemini-2.0-flash-thinking-exp-1219"
  },
  "phi-3-small-8k": {
    "HarProvider": "phi-3-small-8k-instruct",
    "LegacyLMArena": "phi-3-small-8k-instruct"
  },
  "deepseek-v2.5": {
    "HarProvider": [
      "deepseek-v2.5-1210",
      "deepseek-v2.5"
    ],
    "LegacyLMArena": [
      "deepseek-v2.5-1210",
      "deepseek-v2.5"
    ]
  },
  "gemma-3-12b-it": {
    "HarProvider": "gemma-3-12b-it",
    "DeepInfraChat": "google/gemma-3-12b-it",
    "LegacyLMArena": "gemma-3-12b-it",
    "GeminiPro": "gemma-3-12b-it"
  },
  "aya-expanse-32b": {
    "HarProvider": "c4ai-aya-expanse-32b",
    "LegacyLMArena": "c4ai-aya-expanse-32b"
  },
  "nemotron-4-340b": {
    "HarProvider": [
      "nemotron-4-340b-instruct",
      "nemotron-4-340b"
    ],
    "LegacyLMArena": [
      "nemotron-4-340b-instruct",
      "nemotron-4-340b"
    ]
  },
  "openchat-3.5-0106": {
    "HarProvider": "openchat-3.5-0106",
    "LegacyLMArena": "openchat-3.5-0106",
    "Cloudflare": "@cf/openchat/openchat-3.5-0106"
  },
  "oasst-pythia-12b": {
    "HarProvider": "oasst-pythia-12b",
    "LegacyLMArena": "oasst-pythia-12b"
  },
  "gemini-exp-1206": {
    "HarProvider": "gemini-exp-1206",
    "LegacyLMArena": "gemini-exp-1206"
  },
  "mistral-small-24b-instruct-2501": {
    "HarProvider": "mistral-small-24b-instruct-2501",
    "LegacyLMArena": "mistral-small-24b-instruct-2501"
  },
  "gpt-4-0613": {
    "HarProvider": "gpt-4-0613",
    "LegacyLMArena": "gpt-4-0613"
  },
  "gemma-2-2b-it": {
    "HarProvider": "gemma-2-2b-it",
    "LegacyLMArena": "gemma-2-2b-it"
  },
  "llama2-70b-steerlm": {
    "HarProvider": "llama2-70b-steerlm-chat",
    "LegacyLMArena": "llama2-70b-steerlm-chat"
  },
  "deepseek-llm-67b": {
    "HarProvider": "deepseek-llm-67b-chat",
    "LegacyLMArena": "deepseek-llm-67b-chat"
  },
  "zephyr-orpo-141b-a35b-v0.1": {
    "HarProvider": "zephyr-orpo-141b-A35b-v0.1",
    "LegacyLMArena": "zephyr-orpo-141b-A35b-v0.1"
  },
  "llama-4-maverick-17b-128e": {
    "HarProvider": "llama-4-maverick-17b-128e-instruct",
    "LegacyLMArena": "llama-4-maverick-17b-128e-instruct",
    "LMArenaBeta": "llama-4-maverick-17b-128e-instruct"
  },
  "hunyuan-standard-256k": {
    "HarProvider": "hunyuan-standard-256k",
    "LegacyLMArena": "hunyuan-standard-256k"
  },
  "yi-large": {
    "HarProvider": "yi-large",
    "LegacyLMArena": "yi-large"
  },
  "fastchat-t5-3b": {
    "HarProvider": "fastchat-t5-3b",
    "LegacyLMArena": "fastchat-t5-3b"
  },
  "codellama-34b": {
    "HarProvider": "codellama-34b-instruct",
    "LegacyLMArena": "codellama-34b-instruct"
  },
  "mpt-7b": {
    "HarProvider": "mpt-7b-chat",
    "LegacyLMArena": "mpt-7b-chat"
  },
  "claude-2.1": {
    "HarProvider": "claude-2.1",
    "LegacyLMArena": "claude-2.1",
    "PuterJS": [
      "openrouter:anthropic/claude-2.1:beta",
      "openrouter:anthropic/claude-2.1"
    ]
  },
  "deepseek-coder": {
    "HarProvider": "deepseek-coder-v2",
    "LegacyLMArena": "deepseek-coder-v2",
    "PuterJS": [
      "openrouter:deepseek/deepseek-coder"
    ]
  },
  "llama-13b": {
    "HarProvider": "llama-13b",
    "LegacyLMArena": "llama-13b"
  },
  "solar-10.7b-instruct-v1.0": {
    "HarProvider": "solar-10.7b-instruct-v1.0",
    "LegacyLMArena": "solar-10.7b-instruct-v1.0"
  },
  "qwen-max-0428": {
    "HarProvider": "qwen-max-0428",
    "LegacyLMArena": "qwen-max-0428"
  },
  "qwen1.5-72b": {
    "HarProvider": "qwen1.5-72b-chat",
    "LegacyLMArena": "qwen1.5-72b-chat"
  },
  "gemma-7b-it": {
    "HarProvider": "gemma-7b-it",
    "LegacyLMArena": "gemma-7b-it"
  },
  "gpt-3.5-turbo-1106": {
    "HarProvider": "gpt-3.5-turbo-1106",
    "LegacyLMArena": "gpt-3.5-turbo-1106"
  },
  "qwen-14b": {
    "HarProvider": "qwen-14b-chat",
    "LegacyLMArena": "qwen-14b-chat"
  },
  "gpt-4-1106": {
    "HarProvider": "gpt-4-1106-preview",
    "LegacyLMArena": "gpt-4-1106-preview"
  },
  "command-r24": {
    "HarProvider": "command-r-08-2024",
    "LegacyLMArena": "command-r-08-2024",
    "HuggingSpace": "command-r-08-2024"
  },
  "stablelm-tuned-alpha-7b": {
    "HarProvider": "stablelm-tuned-alpha-7b",
    "LegacyLMArena": "stablelm-tuned-alpha-7b"
  },
  "granite-3.0-2b": {
    "HarProvider": "granite-3.0-2b-instruct",
    "LegacyLMArena": "granite-3.0-2b-instruct"
  },
  "claude-3-5-sonnet": {
    "HarProvider": "claude-3-5-sonnet-20240620",
    "LegacyLMArena": "claude-3-5-sonnet-20240620",
    "LMArenaBeta": "claude-3-5-sonnet-20241022",
    "PuterJS": "claude-3-5-sonnet-20241022"
  },
  "phi-3-mini-4k": {
    "HarProvider": "phi-3-mini-4k-instruct",
    "LegacyLMArena": "phi-3-mini-4k-instruct"
  },
  "deepseek-v2.5-1210": {
    "HarProvider": "deepseek-v2.5-1210",
    "LegacyLMArena": "deepseek-v2.5-1210"
  },
  "mistral-large-2411": {
    "HarProvider": "mistral-large-2411",
    "LegacyLMArena": "mistral-large-2411",
    "PuterJS": "mistral-large-2411"
  },
  "alpaca-13b": {
    "HarProvider": "alpaca-13b",
    "LegacyLMArena": "alpaca-13b"
  },
  "gemini-2.0-pro-exp": {
    "HarProvider": "gemini-2.0-pro-exp-02-05",
    "LegacyLMArena": "gemini-2.0-pro-exp-02-05"
  },
  "glm-4-plus-0111": {
    "HarProvider": "glm-4-plus-0111",
    "LegacyLMArena": "glm-4-plus-0111"
  },
  "phi-3-mini-4k-instruct-june-2024": {
    "HarProvider": "phi-3-mini-4k-instruct-june-2024",
    "LegacyLMArena": "phi-3-mini-4k-instruct-june-2024"
  },
  "reka-flash-21b": {
    "HarProvider": "reka-flash-21b-********",
    "LegacyLMArena": "reka-flash-21b-********"
  },
  "koala-13b": {
    "HarProvider": "koala-13b",
    "LegacyLMArena": "koala-13b"
  },
  "zephyr-7b-alpha": {
    "HarProvider": "zephyr-7b-alpha",
    "LegacyLMArena": "zephyr-7b-alpha"
  },
  "amazon-nova-lite-v1.0": {
    "HarProvider": "amazon-nova-lite-v1.0",
    "LegacyLMArena": "amazon-nova-lite-v1.0"
  },
  "wizardlm-13b": {
    "HarProvider": "wizardlm-13b",
    "LegacyLMArena": "wizardlm-13b"
  },
  "falcon-180b": {
    "HarProvider": "falcon-180b-chat",
    "LegacyLMArena": "falcon-180b-chat"
  },
  "deepseek-coder-v2-0724": {
    "HarProvider": "deepseek-coder-v2-0724",
    "LegacyLMArena": "deepseek-coder-v2-0724"
  },
  "wizardlm-70b": {
    "HarProvider": "wizardlm-70b",
    "LegacyLMArena": "wizardlm-70b"
  },
  "qwen-max-0919": {
    "HarProvider": "qwen-max-0919",
    "LegacyLMArena": "qwen-max-0919"
  },
  "qwen-plus-0828": {
    "HarProvider": "qwen-plus-0828",
    "LegacyLMArena": "qwen-plus-0828"
  },
  "gemini-exp-1114": {
    "HarProvider": "gemini-exp-1114",
    "LegacyLMArena": "gemini-exp-1114"
  },
  "mistral-large-2402": {
    "HarProvider": "mistral-large-2402",
    "LegacyLMArena": "mistral-large-2402"
  },
  "snowflake-arctic": {
    "HarProvider": "snowflake-arctic-instruct",
    "LegacyLMArena": "snowflake-arctic-instruct"
  },
  "yi-lightning-lite": {
    "HarProvider": "yi-lightning-lite",
    "LegacyLMArena": "yi-lightning-lite"
  },
  "yi-34b": {
    "HarProvider": "yi-34b-chat",
    "LegacyLMArena": "yi-34b-chat"
  },
  "grok-2-mini": {
    "HarProvider": "grok-2-mini-2024-08-13",
    "LegacyLMArena": "grok-2-mini-2024-08-13"
  },
  "gemini-pro-dev-api": {
    "HarProvider": "gemini-pro-dev-api",
    "LegacyLMArena": "gemini-pro-dev-api"
  },
  "llama-3.1-tulu-3-8b": {
    "HarProvider": "llama-3.1-tulu-3-8b",
    "LegacyLMArena": "llama-3.1-tulu-3-8b"
  },
  "granite-3.1-8b": {
    "HarProvider": "granite-3.1-8b-instruct",
    "LegacyLMArena": "granite-3.1-8b-instruct"
  },
  "glm-4-0520": {
    "HarProvider": "glm-4-0520",
    "LegacyLMArena": "glm-4-0520"
  },
  "gemma-2-9b-it": {
    "HarProvider": "gemma-2-9b-it",
    "DeepInfraChat": "google/gemma-2-9b-it",
    "LegacyLMArena": "gemma-2-9b-it"
  },
  "gemma-2-27b-it": {
    "HarProvider": "gemma-2-27b-it",
    "DeepInfraChat": "google/gemma-2-27b-it",
    "LegacyLMArena": "gemma-2-27b-it"
  },
  "claude-2.0": {
    "HarProvider": "claude-2.0",
    "LegacyLMArena": "claude-2.0",
    "PuterJS": [
      "openrouter:anthropic/claude-2.0:beta",
      "openrouter:anthropic/claude-2.0"
    ]
  },
  "glm-4-plus": {
    "HarProvider": [
      "glm-4-plus-0111",
      "glm-4-plus"
    ],
    "LegacyLMArena": [
      "glm-4-plus-0111",
      "glm-4-plus"
    ]
  },
  "qwen2.5-72b": {
    "HarProvider": "qwen2.5-72b-instruct",
    "HuggingFace": "Qwen/Qwen2.5-72B-Instruct",
    "LegacyLMArena": "qwen2.5-72b-instruct"
  },
  "llama-3.1-tulu-3-70b": {
    "HarProvider": "llama-3.1-tulu-3-70b",
    "LegacyLMArena": "llama-3.1-tulu-3-70b"
  },
  "openchat-3.5": {
    "HarProvider": "openchat-3.5",
    "LegacyLMArena": "openchat-3.5"
  },
  "reka-flash-21b-********-online": {
    "HarProvider": "reka-flash-21b-********-online",
    "LegacyLMArena": "reka-flash-21b-********-online"
  },
  "gemma-1.1-7b-it": {
    "HarProvider": "gemma-1.1-7b-it",
    "DeepInfraChat": "google/gemma-1.1-7b-it",
    "LegacyLMArena": "gemma-1.1-7b-it"
  },
  "claude-3.7-sonnet": {
    "HarProvider": "claude-3-7-sonnet-20250219",
    "LegacyLMArena": "claude-3-7-sonnet-20250219",
    "PuterJS": [
      "claude-3-7-sonnet-20250219",
      "claude-3-7-sonnet-latest",
      "openrouter:anthropic/claude-3.7-sonnet",
      "openrouter:anthropic/claude-3.7-sonnet:beta"
    ]
  },
  "claude-3.7-sonnet-thinking": {
    "HarProvider": "claude-3-7-sonnet-20250219-thinking-32k",
    "LegacyLMArena": "claude-3-7-sonnet-20250219-thinking-32k",
    "PuterJS": "openrouter:anthropic/claude-3.7-sonnet:thinking"
  },
  "gemini-2.0-pro": {
    "HarProvider": "gemini-2.0-pro-exp-02-05",
    "LegacyLMArena": "gemini-2.0-pro-exp-02-05"
  },
  "qwen-plus": {
    "HarProvider": [
      "qwen-plus-0125",
      "qwen-plus-0828",
      "qwen-plus-0125-exp"
    ],
    "LegacyLMArena": [
      "qwen-plus-0125",
      "qwen-plus-0828",
      "qwen-plus-0125-exp"
    ],
    "PuterJS": "openrouter:qwen/qwen-plus"
  },
  "claude-3.5-sonnet": {
    "HarProvider": [
      "claude-3-5-sonnet-20241022",
      "claude-3-5-sonnet-20240620"
    ],
    "LegacyLMArena": [
      "claude-3-5-sonnet-20241022",
      "claude-3-5-sonnet-20240620"
    ],
    "PuterJS": [
      "claude-3-5-sonnet-20241022",
      "claude-3-5-sonnet-latest",
      "claude-3-5-sonnet-20240620",
      "openrouter:anthropic/claude-3.5-sonnet-20240620:beta",
      "openrouter:anthropic/claude-3.5-sonnet-20240620",
      "openrouter:anthropic/claude-3.5-sonnet:beta",
      "openrouter:anthropic/claude-3.5-sonnet"
    ]
  },
  "qwen-2.5-plus": {
    "HarProvider": "qwen2.5-plus-1127",
    "LegacyLMArena": "qwen2.5-plus-1127"
  },
  "qwen-max": {
    "HarProvider": [
      "qwen-max-0919",
      "qwen-max-0428",
      "qwen-max-2025-01-25"
    ],
    "LegacyLMArena": [
      "qwen-max-0919",
      "qwen-max-0428",
      "qwen-max-2025-01-25"
    ],
    "LMArenaBeta": "qwen-max-2025-01-25",
    "PuterJS": "openrouter:qwen/qwen-max"
  },
  "qwen-2.5-vl-32b": {
    "HarProvider": "qwen2.5-vl-32b-instruct",
    "LegacyLMArena": "qwen2.5-vl-32b-instruct",
    "PuterJS": [
      "openrouter:qwen/qwen2.5-vl-32b-instruct:free",
      "openrouter:qwen/qwen2.5-vl-32b-instruct"
    ]
  },
  "nemotron-49b": {
    "HarProvider": "llama-3.3-nemotron-49b-super-v1",
    "LegacyLMArena": "llama-3.3-nemotron-49b-super-v1",
    "PuterJS": [
      "openrouter:nvidia/llama-3.3-nemotron-super-49b-v1:free",
      "openrouter:nvidia/llama-3.3-nemotron-super-49b-v1"
    ]
  },
  "mistral-large": {
    "HarProvider": [
      "mistral-large-2407",
      "mistral-large-2411",
      "mistral-large-2402"
    ],
    "LegacyLMArena": [
      "mistral-large-2407",
      "mistral-large-2411",
      "mistral-large-2402"
    ],
    "PuterJS": [
      "openrouter:mistralai/mistral-large",
      "openrouter:mistralai/mistral-large-2411",
      "openrouter:mistralai/mistral-large-2407",
      "openrouter:mistralai/pixtral-large-2411"
    ]
  },
  "pixtral-large": {
    "HarProvider": "pixtral-large-2411",
    "LegacyLMArena": "pixtral-large-2411",
    "PuterJS": [
      "pixtral-large-2411",
      "pixtral-large-latest",
      "mistral-large-pixtral-2411"
    ]
  },
  "nemotron-253b": {
    "HarProvider": "llama-3.1-nemotron-ultra-253b-v1",
    "LegacyLMArena": "llama-3.1-nemotron-ultra-253b-v1",
    "PuterJS": "openrouter:nvidia/llama-3.1-nemotron-ultra-253b-v1:free"
  },
  "tulu-3-70b": {
    "HarProvider": "llama-3.1-tulu-3-70b",
    "LegacyLMArena": "llama-3.1-tulu-3-70b"
  },
  "claude-3.5-haiku": {
    "HarProvider": "claude-3-5-haiku-20241022",
    "LegacyLMArena": "claude-3-5-haiku-20241022",
    "PuterJS": [
      "openrouter:anthropic/claude-3.5-haiku:beta",
      "openrouter:anthropic/claude-3.5-haiku",
      "openrouter:anthropic/claude-3.5-haiku-20241022:beta",
      "openrouter:anthropic/claude-3.5-haiku-20241022"
    ]
  },
  "deepseek-v2": {
    "HarProvider": "deepseek-v2-api-0628",
    "LegacyLMArena": "deepseek-v2-api-0628"
  },
  "deepseek-coder-v2": {
    "HarProvider": [
      "deepseek-coder-v2-0724",
      "deepseek-coder-v2"
    ],
    "LegacyLMArena": [
      "deepseek-coder-v2-0724",
      "deepseek-coder-v2"
    ]
  },
  "nemotron-51b": {
    "HarProvider": "llama-3.1-nemotron-51b-instruct",
    "LegacyLMArena": "llama-3.1-nemotron-51b-instruct"
  },
  "glm-4": {
    "HarProvider": [
      "glm-4-0520",
      "glm-4-0116"
    ],
    "LegacyLMArena": [
      "glm-4-0520",
      "glm-4-0116"
    ],
    "PuterJS": [
      "openrouter:thudm/glm-4-32b:free",
      "openrouter:thudm/glm-4-32b",
      "openrouter:thudm/glm-4-9b:free"
    ]
  },
  "tulu-3-8b": {
    "HarProvider": "llama-3.1-tulu-3-8b",
    "LegacyLMArena": "llama-3.1-tulu-3-8b"
  },
  "codestral": {
    "HarProvider": "codestral-2405",
    "LegacyLMArena": "codestral-2405",
    "PuterJS": [
      "codestral-2501",
      "codestral-latest",
      "codestral-2412",
      "codestral-2411-rc5",
      "openrouter:mistralai/codestral-2501",
      "openrouter:mistralai/codestral-mamba"
    ]
  },
  "qwen-1.5-110b": {
    "HarProvider": "qwen1.5-110b-chat",
    "LegacyLMArena": "qwen1.5-110b-chat"
  },
  "qwen-1.5-72b": {
    "HarProvider": "qwen1.5-72b-chat",
    "LegacyLMArena": "qwen1.5-72b-chat"
  },
  "gemma-2-2b": {
    "HarProvider": "gemma-2-2b-it",
    "LegacyLMArena": "gemma-2-2b-it"
  },
  "qwen-vl-max": {
    "HarProvider": [
      "qwen-vl-max-1119",
      "qwen-vl-max-0809"
    ],
    "LegacyLMArena": [
      "qwen-vl-max-1119",
      "qwen-vl-max-0809"
    ],
    "PuterJS": "openrouter:qwen/qwen-vl-max"
  },
  "mixtral-8x22b": {
    "HarProvider": "mixtral-8x22b-instruct-v0.1",
    "LegacyLMArena": "mixtral-8x22b-instruct-v0.1",
    "PuterJS": [
      "open-mixtral-8x22b",
      "open-mixtral-8x22b-2404",
      "openrouter:mistralai/mixtral-8x7b-instruct",
      "openrouter:mistralai/mixtral-8x22b-instruct"
    ]
  },
  "qwen-1.5-32b": {
    "HarProvider": "qwen1.5-32b-chat",
    "LegacyLMArena": "qwen1.5-32b-chat"
  },
  "qwen-1.5-14b": {
    "HarProvider": "qwen1.5-14b-chat",
    "LegacyLMArena": "qwen1.5-14b-chat",
    "Cloudflare": "@cf/qwen/qwen1.5-14b-chat-awq"
  },
  "qwen-1.5-4b": {
    "HarProvider": "qwen1.5-4b-chat",
    "LegacyLMArena": "qwen1.5-4b-chat"
  },
  "phi-3-medium": {
    "HarProvider": "phi-3-medium-4k-instruct",
    "LegacyLMArena": "phi-3-medium-4k-instruct",
    "PuterJS": "openrouter:microsoft/phi-3-medium-128k-instruct"
  },
  "phi-3-small": {
    "HarProvider": "phi-3-small-8k-instruct",
    "LegacyLMArena": "phi-3-small-8k-instruct"
  },
  "phi-3-mini": {
    "HarProvider": [
      "phi-3-mini-4k-instruct-june-2024",
      "phi-3-mini-4k-instruct",
      "phi-3-mini-128k-instruct"
    ],
    "LegacyLMArena": [
      "phi-3-mini-4k-instruct-june-2024",
      "phi-3-mini-4k-instruct",
      "phi-3-mini-128k-instruct"
    ],
    "PuterJS": "openrouter:microsoft/phi-3-mini-128k-instruct"
  },
  "tulu-2-70b": {
    "HarProvider": "tulu-2-dpo-70b",
    "LegacyLMArena": "tulu-2-dpo-70b"
  },
  "deepseek-67b": {
    "HarProvider": "deepseek-llm-67b-chat",
    "LegacyLMArena": "deepseek-llm-67b-chat"
  },
  "openhermes-2.5-7b": {
    "HarProvider": "openhermes-2.5-mistral-7b",
    "LegacyLMArena": "openhermes-2.5-mistral-7b"
  },
  "gpt-3.5-turbo": {
    "HarProvider": [
      "gpt-3.5-turbo-0613",
      "gpt-3.5-turbo-0314",
      "gpt-3.5-turbo-0125",
      "gpt-3.5-turbo-1106"
    ],
    "LegacyLMArena": [
      "gpt-3.5-turbo-0613",
      "gpt-3.5-turbo-0314",
      "gpt-3.5-turbo-0125",
      "gpt-3.5-turbo-1106"
    ],
    "PuterJS": [
      "openrouter:openai/gpt-3.5-turbo-0613",
      "openrouter:openai/gpt-3.5-turbo-1106",
      "openrouter:openai/gpt-3.5-turbo-0125",
      "openrouter:openai/gpt-3.5-turbo",
      "openrouter:openai/gpt-3.5-turbo-instruct",
      "openrouter:openai/gpt-3.5-turbo-16k"
    ]
  },
  "deepseek-llama-3.3-70b": {
    "LambdaChat": "deepseek-llama3.3-70b"
  },
  "apriel-5b": {
    "LambdaChat": "apriel-5b-instruct"
  },
  "hermes-3-llama-3.1-405b": {
    "LambdaChat": "hermes-3-llama-3.1-405b-fp8"
  },
  "hermes3-405b-fp8-128k": {
    "LambdaChat": "hermes3-405b-fp8-128k"
  },
  "llama-3.3-70b-instruct": {
    "LambdaChat": "llama3.3-70b-instruct-fp8",
    "HarProvider": "llama-3.3-70b-instruct",
    "LegacyLMArena": "llama-3.3-70b-instruct",
    "LMArenaBeta": "llama-3.3-70b-instruct"
  },
  "qwen25-coder-32b": {
    "LambdaChat": "qwen25-coder-32b-instruct"
  },
  "llama-4-maverick-17b-128e-instruct": {
    "LambdaChat": "llama-4-maverick-17b-128e-instruct-fp8",
    "DeepInfraChat": "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8",
    "HarProvider": "llama-4-maverick-17b-128e-instruct",
    "LegacyLMArena": "llama-4-maverick-17b-128e-instruct",
    "LMArenaBeta": "llama-4-maverick-17b-128e-instruct"
  },
  "qwen-332b": {
    "LambdaChat": "qwen3-32b-fp8",
    "DeepInfraChat": "Qwen/Qwen3-32B",
    "HuggingFace": "Qwen/Qwen3-32B"
  },
  "hermes-3": {
    "LambdaChat": "hermes3-405b-fp8-128k"
  },
  "hermes-3-405b": {
    "LambdaChat": [
      "hermes3-405b-fp8-128k",
      "hermes-3-llama-3.1-405b-fp8"
    ],
    "PuterJS": "openrouter:nousresearch/hermes-3-llama-3.1-405b"
  },
  "dolphin-2.6-mixtral-8x7b": {
    "DeepInfraChat": "cognitivecomputations/dolphin-2.6-mixtral-8x7b"
  },
  "dolphin-2.9.1-llama-3-70b": {
    "DeepInfraChat": "cognitivecomputations/dolphin-2.9.1-llama-3-70b"
  },
  "codegemma-7b-it": {
    "DeepInfraChat": "google/codegemma-7b-it"
  },
  "lzlv.70b.fp16.hf": {
    "DeepInfraChat": "lizpreciatior/lzlv_70b_fp16_hf"
  },
  "llama-3.3-70b-instruct-turbo": {
    "DeepInfraChat": "meta-llama/Llama-3.3-70B-Instruct-Turbo"
  },
  "llama-3.1-70b-instruct-turbo": {
    "DeepInfraChat": "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo"
  },
  "mistral-small-3.1-24b-instruct-2503": {
    "DeepInfraChat": "mistralai/Mistral-Small-3.1-24B-Instruct-2503",
    "LMArenaBeta": "mistral-small-3.1-24b-instruct-2503"
  },
  "qwen-330b-a3b": {
    "DeepInfraChat": "Qwen/Qwen3-30B-A3B",
    "HuggingFace": "Qwen/Qwen3-30B-A3B",
    "LMArenaBeta": "qwen3-30b-a3b"
  },
  "qwen-314b": {
    "DeepInfraChat": "Qwen/Qwen3-14B"
  },
  "llama-3.2-90b-vision": {
    "DeepInfraChat": "meta-llama/Llama-3.2-90B-Vision-Instruct"
  },
  "kimi-k2": {
    "HuggingFace": "moonshotai/Kimi-K2-Instruct"
  },
  "smollm3-3b": {
    "HuggingFace": "HuggingFaceTB/SmolLM3-3B"
  },
  "devstral-small-2507": {
    "HuggingFace": "mistralai/Devstral-Small-2507",
    "PuterJS": "devstral-small-2507"
  },
  "mistral-7b-instruct-v0.3": {
    "HuggingFace": "mistralai/Mistral-7B-Instruct-v0.3"
  },
  "qwen-30.6b": {
    "HuggingFace": "Qwen/Qwen3-0.6B"
  },
  "qwen-34b": {
    "HuggingFace": "Qwen/Qwen3-4B"
  },
  "qwen-38b": {
    "HuggingFace": "Qwen/Qwen3-8B"
  },
  "nextcoder-32b": {
    "HuggingFace": "microsoft/NextCoder-32B"
  },
  "magistral-small-2506": {
    "HuggingFace": "mistralai/Magistral-Small-2506",
    "PuterJS": "magistral-small-2506"
  },
  "deepseek-r1-0528-qwen-38b": {
    "HuggingFace": "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B"
  },
  "deepswe": {
    "HuggingFace": "agentica-org/DeepSWE-Preview"
  },
  "tinyllama-1.1b-chat-v1.0": {
    "HuggingFace": "TinyLlama/TinyLlama-1.1B-Chat-v1.0"
  },
  "arch-router-1.5b": {
    "HuggingFace": "katanemo/Arch-Router-1.5B"
  },
  "llama-3.2-11b-vision": {
    "HuggingFace": "meta-llama/Llama-3.2-11B-Vision-Instruct",
    "Cloudflare": "@cf/meta/llama-3.2-11b-vision-instruct"
  },
  "qwen-2vl-7b": {
    "HuggingFace": "Qwen/Qwen2-VL-7B-Instruct"
  },
  "llama-3.1-nemotron-70b-instruct": {
    "HuggingFace": "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF",
    "HarProvider": "llama-3.1-nemotron-70b-instruct",
    "LegacyLMArena": "llama-3.1-nemotron-70b-instruct"
  },
  "mistral-nemo-instruct-2407": {
    "HuggingFace": "mistralai/Mistral-Nemo-Instruct-2407"
  },
  "phi-3.5-mini": {
    "HuggingFace": "microsoft/Phi-3.5-mini-instruct",
    "PuterJS": "openrouter:microsoft/phi-3.5-mini-128k-instruct"
  },
  "stable-diffusion-xl-base-1.0": {
    "HuggingFace": "stabilityai/stable-diffusion-xl-base-1.0"
  },
  "llama-3": {
    "HuggingFace": "meta-llama/Llama-3.3-70B-Instruct"
  },
  "qvq-72b": {
    "HuggingFace": "Qwen/QVQ-72B-Preview"
  },
  "stable-diffusion-3.5-large": {
    "HuggingFace": "stabilityai/stable-diffusion-3.5-large",
    "HuggingFaceMedia": "stabilityai/stable-diffusion-3.5-large"
  },
  "sdxl-1.0": {
    "HuggingFace": "stabilityai/stable-diffusion-xl-base-1.0",
    "HuggingFaceMedia": "stabilityai/stable-diffusion-xl-base-1.0"
  },
  "wan2.1-t2v-14b": {
    "HuggingFaceMedia": "Wan-AI/Wan2.1-T2V-14B"
  },
  "hunyuanvideo": {
    "HuggingFaceMedia": "tencent/HunyuanVideo"
  },
  "wan2.1-t2v-1.3b": {
    "HuggingFaceMedia": "Wan-AI/Wan2.1-T2V-1.3B"
  },
  "cogvideox-5b": {
    "HuggingFaceMedia": "THUDM/CogVideoX-5b"
  },
  "mochi-1": {
    "HuggingFaceMedia": "genmo/mochi-1-preview"
  },
  "ltx-video-0.9.7-distilled": {
    "HuggingFaceMedia": "Lightricks/LTX-Video-0.9.7-distilled"
  },
  "claude-opus-4": {
    "LMArenaBeta": "claude-opus-4-20250514",
    "PuterJS": "claude-opus-4"
  },
  "steve": {
    "LMArenaBeta": "steve"
  },
  "command-a25": {
    "LMArenaBeta": "command-a-03-2025",
    "HuggingSpace": "command-a-03-2025"
  },
  "amazon.nova-pro": {
    "LMArenaBeta": "amazon.nova-pro-v1:0"
  },
  "grok-3-mini-beta": {
    "LMArenaBeta": "grok-3-mini-beta"
  },
  "gemini-2.5-flash-lite-preview-thinking": {
    "LMArenaBeta": "gemini-2.5-flash-lite-preview-06-17-thinking"
  },
  "amazon-nova-experimental": {
    "LMArenaBeta": "amazon-nova-experimental-chat-05-14"
  },
  "magistral-medium-2506": {
    "LMArenaBeta": "magistral-medium-2506",
    "PuterJS": "magistral-medium-2506"
  },
  "x": {
    "LMArenaBeta": "X-preview"
  },
  "stephen": {
    "LMArenaBeta": "stephen-v2"
  },
  "glm-4-air-250414": {
    "LMArenaBeta": "glm-4-air-250414"
  },
  "claude-sonnet-4": {
    "LMArenaBeta": "claude-sonnet-4-20250514",
    "PuterJS": "claude-sonnet-4"
  },
  "stonebloom": {
    "LMArenaBeta": "stonebloom"
  },
  "minimax-m1": {
    "LMArenaBeta": "minimax-m1"
  },
  "step-1o-turbo-202506": {
    "LMArenaBeta": "step-1o-turbo-202506"
  },
  "claude-sonnet-4-20250514-thinking-32k": {
    "LMArenaBeta": "claude-sonnet-4-20250514-thinking-32k"
  },
  "qwen-3235b-a22b-no-thinking": {
    "LMArenaBeta": "qwen3-235b-a22b-no-thinking"
  },
  "gemma-3n-e4b-it": {
    "LMArenaBeta": "gemma-3n-e4b-it",
    "GeminiPro": "gemma-3n-e4b-it"
  },
  "claude-opus-4-20250514-thinking-16k": {
    "LMArenaBeta": "claude-opus-4-20250514-thinking-16k"
  },
  "stephen-vision-csfix": {
    "LMArenaBeta": "stephen-vision-csfix"
  },
  "mistral-small-2506": {
    "LMArenaBeta": "mistral-small-2506",
    "PuterJS": "mistral-small-2506"
  },
  "wolfstride": {
    "LMArenaBeta": "wolfstride"
  },
  "grok-4-0709": {
    "LMArenaBeta": "grok-4-0709"
  },
  "cresylux": {
    "LMArenaBeta": "cresylux"
  },
  "hunyuan-turbos": {
    "LMArenaBeta": "hunyuan-turbos-20250416"
  },
  "ernie-x1-turbo-32k": {
    "LMArenaBeta": "ernie-x1-turbo-32k-preview"
  },
  "kimi-k2-0711": {
    "LMArenaBeta": "kimi-k2-0711-preview"
  },
  "nettle": {
    "LMArenaBeta": "nettle"
  },
  "clownfish": {
    "LMArenaBeta": "clownfish"
  },
  "octopus": {
    "LMArenaBeta": "octopus"
  },
  "kraken-07152025-1": {
    "LMArenaBeta": "kraken-07152025-1"
  },
  "kraken-07152025-2": {
    "LMArenaBeta": "kraken-07152025-2"
  },
  "folsom-07152025-1": {
    "LMArenaBeta": "folsom-07152025-1"
  },
  "hunyuan-large-vision": {
    "LMArenaBeta": "hunyuan-large-vision"
  },
  "flux-1-kontext-pro": {
    "LMArenaBeta": "flux-1-kontext-pro"
  },
  "gpt-image-1": {
    "LMArenaBeta": "gpt-image-1"
  },
  "flux-1-kontext-max": {
    "LMArenaBeta": "flux-1-kontext-max"
  },
  "imagen-4.0-ultra-generate": {
    "LMArenaBeta": "imagen-4.0-ultra-generate-preview-06-06"
  },
  "imagen-3.0-generate-002": {
    "LMArenaBeta": "imagen-3.0-generate-002"
  },
  "ideogram": {
    "LMArenaBeta": "ideogram-v2"
  },
  "photon": {
    "LMArenaBeta": "photon"
  },
  "step1x-edit": {
    "LMArenaBeta": "step1x-edit"
  },
  "recraft": {
    "LMArenaBeta": "recraft-v3"
  },
  "anonymous-bot-0514": {
    "LMArenaBeta": "anonymous-bot-0514"
  },
  "flux-1.1-pro": {
    "LMArenaBeta": "flux-1.1-pro"
  },
  "ideogram-v3-quality": {
    "LMArenaBeta": "ideogram-v3-quality"
  },
  "imagen-4.0-generate": {
    "LMArenaBeta": "imagen-4.0-generate-preview-06-06"
  },
  "seedream-3": {
    "LMArenaBeta": "seedream-3"
  },
  "seededit-3.0": {
    "LMArenaBeta": "seededit-3.0"
  },
  "flux-1-kontext-dev": {
    "LMArenaBeta": "flux-1-kontext-dev"
  },
  "bagel": {
    "LMArenaBeta": "bagel"
  },
  "gemini-2.0-flash-preview-image-generation": {
    "LMArenaBeta": "gemini-2.0-flash-preview-image-generation"
  },
  "o1-pro": {
    "PuterJS": [
      "o1-pro",
      "openrouter:openai/o1-pro"
    ]
  },
  "claude-opus-4-latest": {
    "PuterJS": "claude-opus-4-latest"
  },
  "claude-sonnet-4-latest": {
    "PuterJS": "claude-sonnet-4-latest"
  },
  "claude-3-7-sonnet-latest": {
    "PuterJS": "claude-3-7-sonnet-latest"
  },
  "claude-3-5-sonnet-latest": {
    "PuterJS": "claude-3-5-sonnet-latest"
  },
  "mistral-medium-latest": {
    "PuterJS": "mistral-medium-latest"
  },
  "ministral-3b-2410": {
    "PuterJS": "ministral-3b-2410"
  },
  "ministral-3b-latest": {
    "PuterJS": "ministral-3b-latest"
  },
  "ministral-8b-latest": {
    "PuterJS": "ministral-8b-latest"
  },
  "open-mistral-7b": {
    "PuterJS": "open-mistral-7b"
  },
  "mistral-tiny": {
    "PuterJS": [
      "mistral-tiny",
      "mistral-tiny-2312",
      "openrouter:mistralai/mistral-tiny"
    ]
  },
  "mistral-tiny-2312": {
    "PuterJS": "mistral-tiny-2312"
  },
  "open-mistral-nemo": {
    "PuterJS": "open-mistral-nemo"
  },
  "open-mistral-nemo-2407": {
    "PuterJS": "open-mistral-nemo-2407"
  },
  "mistral-tiny-2407": {
    "PuterJS": "mistral-tiny-2407"
  },
  "mistral-tiny-latest": {
    "PuterJS": "mistral-tiny-latest"
  },
  "open-mixtral-8x7b": {
    "PuterJS": "open-mixtral-8x7b"
  },
  "mistral-small": {
    "PuterJS": [
      "mistral-small",
      "mistral-small-2312",
      "mistral-small-2503",
      "mistral-small-latest",
      "openrouter:mistralai/mistral-small",
      "openrouter:mistralai/mistral-small-3.1-24b-instruct:free",
      "openrouter:mistralai/mistral-small-3.1-24b-instruct",
      "openrouter:mistralai/mistral-small-24b-instruct-2501:free",
      "openrouter:mistralai/mistral-small-24b-instruct-2501"
    ]
  },
  "mistral-small-2312": {
    "PuterJS": "mistral-small-2312"
  },
  "open-mixtral-8x22b": {
    "PuterJS": "open-mixtral-8x22b"
  },
  "open-mixtral-8x22b-2404": {
    "PuterJS": "open-mixtral-8x22b-2404"
  },
  "mistral-large-latest": {
    "PuterJS": "mistral-large-latest"
  },
  "pixtral-large-2411": {
    "PuterJS": "pixtral-large-2411"
  },
  "pixtral-large-latest": {
    "PuterJS": "pixtral-large-latest"
  },
  "mistral-large-pixtral-2411": {
    "PuterJS": "mistral-large-pixtral-2411"
  },
  "codestral-2501": {
    "PuterJS": "codestral-2501"
  },
  "codestral-latest": {
    "PuterJS": "codestral-latest"
  },
  "codestral-2412": {
    "PuterJS": "codestral-2412"
  },
  "codestral-2411-rc5": {
    "PuterJS": "codestral-2411-rc5"
  },
  "devstral-small-latest": {
    "PuterJS": "devstral-small-latest"
  },
  "pixtral-12b-2409": {
    "PuterJS": "pixtral-12b-2409"
  },
  "pixtral-12b": {
    "PuterJS": [
      "pixtral-12b-2409",
      "pixtral-12b",
      "pixtral-12b-latest",
      "openrouter:mistralai/pixtral-12b"
    ]
  },
  "pixtral-12b-latest": {
    "PuterJS": "pixtral-12b-latest"
  },
  "mistral-small-latest": {
    "PuterJS": "mistral-small-latest"
  },
  "mistral-saba-2502": {
    "PuterJS": "mistral-saba-2502"
  },
  "mistral-saba-latest": {
    "PuterJS": "mistral-saba-latest"
  },
  "magistral-medium-latest": {
    "PuterJS": "magistral-medium-latest"
  },
  "magistral-small-latest": {
    "PuterJS": "magistral-small-latest"
  },
  "mistral-moderation-2411": {
    "PuterJS": "mistral-moderation-2411"
  },
  "mistral-moderation-latest": {
    "PuterJS": "mistral-moderation-latest"
  },
  "mistral-ocr-2505": {
    "PuterJS": "mistral-ocr-2505"
  },
  "mistral-ocr-latest": {
    "PuterJS": "mistral-ocr-latest"
  },
  "grok-beta": {
    "PuterJS": [
      "grok-beta",
      "grok-vision-beta",
      "openrouter:x-ai/grok-beta",
      "openrouter:x-ai/grok-3-beta"
    ]
  },
  "grok-vision-beta": {
    "PuterJS": "grok-vision-beta"
  },
  "grok-3-fast": {
    "PuterJS": "grok-3-fast"
  },
  "grok-3-mini-fast": {
    "PuterJS": "grok-3-mini-fast"
  },
  "grok-2-vision": {
    "PuterJS": "grok-2-vision"
  },
  "deepseek-reasoner": {
    "PuterJS": "deepseek-reasoner"
  },
  "llama-3.3-8b": {
    "PuterJS": "openrouter:meta-llama/llama-3.3-8b-instruct:free"
  },
  "gemini-1.5-8b-flash": {
    "PuterJS": "openrouter:google/gemini-flash-1.5-8b"
  },
  "gemini-2.5-flash-thinking": {
    "PuterJS": "openrouter:google/gemini-2.5-flash-preview:thinking"
  },
  "gemma-3-1b": {
    "PuterJS": "openrouter:google/gemma-3-1b-it:free"
  },
  "gpt-4o-search": {
    "PuterJS": "openrouter:openai/gpt-4o-search-preview"
  },
  "gpt-4o-mini-search": {
    "PuterJS": "openrouter:openai/gpt-4o-mini-search-preview"
  },
  "ministral-8b": {
    "PuterJS": [
      "ministral-8b-2410",
      "ministral-8b-latest",
      "openrouter:mistral/ministral-8b",
      "openrouter:mistralai/ministral-8b"
    ]
  },
  "ministral-3b": {
    "PuterJS": [
      "ministral-3b-2410",
      "ministral-3b-latest",
      "openrouter:mistralai/ministral-3b"
    ]
  },
  "mistral-saba": {
    "PuterJS": "openrouter:mistralai/mistral-saba"
  },
  "hermes-2-pro": {
    "PuterJS": "openrouter:nousresearch/hermes-2-pro-llama-3-8b"
  },
  "hermes-3-70b": {
    "PuterJS": "openrouter:nousresearch/hermes-3-llama-3.1-70b"
  },
  "deephermes-3-8b": {
    "PuterJS": "openrouter:nousresearch/deephermes-3-llama-3-8b-preview:free"
  },
  "deephermes-3-24b": {
    "PuterJS": "openrouter:nousresearch/deephermes-3-mistral-24b-preview:free"
  },
  "phi-4-reasoning": {
    "PuterJS": "openrouter:microsoft/phi-4-reasoning:free"
  },
  "mai-ds-r1": {
    "PuterJS": "openrouter:microsoft/mai-ds-r1:free"
  },
  "claude-2": {
    "PuterJS": [
      "openrouter:anthropic/claude-2:beta",
      "openrouter:anthropic/claude-2"
    ]
  },
  "command": {
    "PuterJS": "openrouter:cohere/command"
  },
  "qwen-vl-plus": {
    "PuterJS": "openrouter:qwen/qwen-vl-plus"
  },
  "qwen-turbo": {
    "PuterJS": "openrouter:qwen/qwen-turbo"
  },
  "qwen-2.5-vl-7b": {
    "PuterJS": [
      "openrouter:qwen/qwen-2.5-vl-7b-instruct:free",
      "openrouter:qwen/qwen-2.5-vl-7b-instruct"
    ]
  },
  "qwen-3-8b": {
    "PuterJS": [
      "openrouter:qwen/qwen3-8b:free",
      "openrouter:qwen/qwen3-8b"
    ],
    "HuggingSpace": "qwen-3-8b"
  },
  "qwen-2.5-coder-7b": {
    "PuterJS": "openrouter:qwen/qwen2.5-coder-7b-instruct"
  },
  "qwen-2.5-vl-3b": {
    "PuterJS": "openrouter:qwen/qwen2.5-vl-3b-instruct:free"
  },
  "deepseek-prover": {
    "PuterJS": "deepseek-prover-v2"
  },
  "deepseek-r1-zero": {
    "PuterJS": "openrouter:deepseek/deepseek-r1-zero:free"
  },
  "deepseek-r1-distill-llama-8b": {
    "PuterJS": "openrouter:deepseek/deepseek-r1-distill-llama-8b"
  },
  "inflection-3-productivity": {
    "PuterJS": "openrouter:inflection/inflection-3-productivity"
  },
  "inflection-3-pi": {
    "PuterJS": "openrouter:inflection/inflection-3-pi"
  },
  "grok-3-beta": {
    "PuterJS": "openrouter:x-ai/grok-3-beta"
  },
  "grok": {
    "PuterJS": [
      "openrouter:x-ai/grok-vision-beta",
      "openrouter:x-ai/grok-2-vision-1212",
      "openrouter:x-ai/grok-2-1212",
      "grok-beta",
      "grok-vision-beta",
      "openrouter:x-ai/grok-beta",
      "openrouter:x-ai/grok-3-beta",
      "openrouter:x-ai/grok-3-mini-beta"
    ]
  },
  "sonar-deep-research": {
    "PuterJS": "openrouter:perplexity/sonar-deep-research"
  },
  "llama-3.1-sonar-small-online": {
    "PuterJS": "openrouter:perplexity/llama-3.1-sonar-small-128k-online"
  },
  "llama-3.1-sonar-large-online": {
    "PuterJS": "openrouter:perplexity/llama-3.1-sonar-large-128k-online"
  },
  "glm-4-32b": {
    "PuterJS": [
      "openrouter:thudm/glm-4-32b:free",
      "openrouter:thudm/glm-4-32b"
    ]
  },
  "glm-z1-32b": {
    "PuterJS": [
      "openrouter:thudm/glm-z1-32b:free",
      "openrouter:thudm/glm-z1-32b"
    ]
  },
  "glm-4-9b": {
    "PuterJS": "openrouter:thudm/glm-4-9b:free"
  },
  "glm-z1-9b": {
    "PuterJS": "openrouter:thudm/glm-z1-9b:free"
  },
  "glm-z1-rumination-32b": {
    "PuterJS": "openrouter:thudm/glm-z1-rumination-32b"
  },
  "minimax": {
    "PuterJS": "openrouter:minimax/minimax-01"
  },
  "dolphin-3.0-r1-24b": {
    "PuterJS": "openrouter:cognitivecomputations/dolphin3.0-r1-mistral-24b:free"
  },
  "dolphin-3.0-24b": {
    "PuterJS": "openrouter:cognitivecomputations/dolphin3.0-mistral-24b:free"
  },
  "dolphin-8x22b": {
    "PuterJS": "openrouter:cognitivecomputations/dolphin-mixtral-8x22b"
  },
  "deepcoder-14b": {
    "PuterJS": "openrouter:agentica-org/deepcoder-14b-preview:free"
  },
  "kimi-vl-thinking": {
    "PuterJS": "openrouter:moonshotai/kimi-vl-a3b-thinking:free"
  },
  "moonlight-16b": {
    "PuterJS": "openrouter:moonshotai/moonlight-16b-a3b-instruct:free"
  },
  "qwerky-72b": {
    "PuterJS": "openrouter:featherless/qwerky-72b:free"
  },
  "lfm-7b": {
    "PuterJS": "openrouter:liquid/lfm-7b"
  },
  "lfm-3b": {
    "PuterJS": "openrouter:liquid/lfm-3b"
  },
  "deepseek-chat": {
    "PuterJS": [
      "deepseek-chat",
      "openrouter:deepseek/deepseek-chat:free",
      "openrouter:deepseek/deepseek-chat"
    ],
    "DeepSeekAPI": "deepseek-v3"
  },
  "deepseek-coder-6.7b-base": {
    "Cloudflare": "@hf/thebloke/deepseek-coder-6.7b-base-awq"
  },
  "deepseek-coder-6.7b": {
    "Cloudflare": "@hf/thebloke/deepseek-coder-6.7b-instruct-awq"
  },
  "deepseek-math-7b": {
    "Cloudflare": "@cf/deepseek-ai/deepseek-math-7b-instruct"
  },
  "deepseek-distill-qwen-32b": {
    "Cloudflare": "@cf/deepseek-ai/deepseek-r1-distill-qwen-32b"
  },
  "discolm-german-7b": {
    "Cloudflare": "discolm-german-7b-v1"
  },
  "falcon-7b": {
    "Cloudflare": "@cf/tiiuae/falcon-7b-instruct"
  },
  "gemma-7b": {
    "Cloudflare": "@hf/google/gemma-7b-it"
  },
  "hermes-2-pro-mistral-7b": {
    "Cloudflare": "@hf/nousresearch/hermes-2-pro-mistral-7b"
  },
  "llama-2-7b-fp16": {
    "Cloudflare": "@cf/meta/llama-2-7b-chat-fp16"
  },
  "llama-guard-3-8b": {
    "Cloudflare": "@cf/meta/llama-guard-3-8b"
  },
  "llamaguard-7b": {
    "Cloudflare": "@hf/thebloke/llamaguard-7b-awq"
  },
  "mistral-7b-v0.1": {
    "Cloudflare": "@hf/thebloke/mistral-7b-instruct-v0.1-awq"
  },
  "mistral-7b-v0.2": {
    "Cloudflare": "@hf/mistral/mistral-7b-instruct-v0.2"
  },
  "neural-7b-v3-1": {
    "Cloudflare": "@hf/thebloke/neural-chat-7b-v3-1-awq"
  },
  "phi-2": {
    "Cloudflare": "@cf/microsoft/phi-2"
  },
  "qwen1.5-0.5b": {
    "Cloudflare": "@cf/qwen/qwen1.5-0.5b-chat"
  },
  "qwen-1.5-1.8b": {
    "Cloudflare": "@cf/qwen/qwen1.5-1.8b-chat"
  },
  "sqlcoder-7b-2": {
    "Cloudflare": "@cf/defog/sqlcoder-7b-2"
  },
  "tinyllama-1.1b-v1.0": {
    "Cloudflare": "@cf/tinyllama/tinyllama-1.1b-chat-v1.0"
  },
  "una-cybertron-7b-v2": {
    "Cloudflare": "una-cybertron-7b-v2-bf16"
  },
  "discolm-german-7b-v1": {
    "Cloudflare": "@cf/thebloke/discolm-german-7b-v1-awq"
  },
  "una-cybertron-7b-v2-bf16": {
    "Cloudflare": "@cf/fblgit/una-cybertron-7b-v2-bf16"
  },
  "command-r7b24": {
    "HuggingSpace": "command-r7b-12-2024"
  },
  "command-r7b-arabic25": {
    "HuggingSpace": "command-r7b-arabic-02-2025"
  },
  "janus-pro-7b-image": {
    "HuggingSpace": "janus-pro-7b-image"
  },
  "qwen-3-30b-a3b": {
    "HuggingSpace": "qwen-3-30b-a3b"
  },
  "video": {
    "Video": "video"
  }
}
models_count = {
  "gpt-4": 10,
  "gpt-4o": 7,
  "gpt-4o-mini": 9,
  "o1": 7,
  "o1-mini": 5,
  "o3-mini": 5,
  "o3-mini-high": 5,
  "o4-mini": 6,
  "o4-mini-high": 2,
  "gpt-4.1": 6,
  "gpt-4.1-mini": 8,
  "gpt-4.1-nano": 5,
  "gpt-4.5": 2,
  "dall-e-3": 6,
  "gpt-image": 2,
  "llama-2-7b": 3,
  "llama-2-70b": 4,
  "llama-3-8b": 6,
  "llama-3-70b": 4,
  "llama-3.1-8b": 7,
  "llama-3.1-70b": 4,
  "llama-3.1-405b": 4,
  "llama-3.2-1b": 4,
  "llama-3.2-3b": 6,
  "llama-3.2-11b": 3,
  "llama-3.2-90b": 3,
  "llama-3.3-70b": 10,
  "llama-4-scout": 8,
  "llama-4-maverick": 7,
  "mistral-7b": 4,
  "mixtral-8x7b": 4,
  "mixtral-8x22b": 3,
  "mistral-nemo": 2,
  "mistral-small-24b": 3,
  "mistral-small-3.1-24b": 5,
  "hermes-2-dpo": 4,
  "phi-3.5-mini": 2,
  "phi-4": 6,
  "phi-4-multimodal": 3,
  "phi-4-reasoning-plus": 2,
  "wizardlm-2-8x22b": 2,
  "gemini-1.5-flash": 5,
  "gemini-1.5-pro": 5,
  "gemini-2.0-flash": 5,
  "gemini-2.0-flash-thinking": 4,
  "gemini-2.5-flash": 6,
  "gemini-2.5-pro": 5,
  "gemma-2-27b": 6,
  "gemma-3-12b": 5,
  "gemma-3-27b": 5,
  "command-r": 4,
  "command-r-plus": 5,
  "command-r7b": 2,
  "command-a": 4,
  "qwen-1.5-7b": 3,
  "qwen-2-72b": 6,
  "qwen-2-vl-7b": 2,
  "qwen-2.5-7b": 2,
  "qwen-2.5-72b": 5,
  "qwen-2.5-coder-32b": 8,
  "qwen-2.5-max": 3,
  "qwen-2.5-vl-72b": 4,
  "qwen-3-235b": 6,
  "qwen-3-32b": 7,
  "qwen-3-30b": 5,
  "qwen-3-14b": 3,
  "qwen-3-4b": 2,
  "qwen-3-1.7b": 2,
  "qwen-3-0.6b": 2,
  "qwq-32b": 8,
  "deepseek-v3": 8,
  "deepseek-r1": 9,
  "deepseek-r1-distill-llama-70b": 3,
  "deepseek-r1-distill-qwen-1.5b": 2,
  "deepseek-r1-distill-qwen-14b": 2,
  "deepseek-r1-distill-qwen-32b": 3,
  "deepseek-prover-v2": 2,
  "deepseek-v3-0324": 7,
  "deepseek-r1-0528": 4,
  "janus-pro-7b": 2,
  "grok-2": 4,
  "grok-3": 5,
  "grok-3-mini": 2,
  "sonar": 2,
  "sonar-pro": 2,
  "sonar-reasoning": 2,
  "sonar-reasoning-pro": 2,
  "r1-1776": 3,
  "nemotron-70b": 6,
  "lfm-40b": 2,
  "sdxl-turbo": 5,
  "sd-3.5-large": 4,
  "flux": 6,
  "flux-pro": 3,
  "flux-dev": 6,
  "flux-schnell": 5,
  "flux-kontext-pro": 2,
  "gemini-2.5-pro-exp": 2,
  "gemini-2.0-flash-thinking-exp": 3,
  "gemini-2.0-flash-exp": 2,
  "chatgpt-4o-latest": 3,
  "o3": 4,
  "claude-3-7-sonnet": 4,
  "claude-3-7-sonnet-20250219-thinking-32k": 3,
  "llama-4-maverick-17b-128e": 3,
  "gemini-2.0-flash-001": 3,
  "gemini-2.0-flash-lite": 3,
  "gemma-3-27b-it": 5,
  "gemma-3-12b-it": 4,
  "gemma-3-4b-it": 4,
  "claude-3-5-sonnet": 4,
  "command-a25": 2,
  "claude-3-5-haiku": 3,
  "mistral-small-24b-instruct-2501": 2,
  "amazon-nova-pro-v1.0": 2,
  "amazon-nova-lite-v1.0": 2,
  "amazon-nova-micro-v1.0": 2,
  "qwen-max": 4,
  "qwen2.5-vl-72b": 2,
  "gemini-1.5-pro-002": 2,
  "gemini-1.5-flash-002": 2,
  "gemini-1.5-flash-8b-001": 2,
  "gemini-1.5-pro-001": 2,
  "gemini-1.5-flash-001": 2,
  "llama-3.1-405b-instruct": 2,
  "llama-3.1-nemotron-70b": 3,
  "hunyuan-turbos": 2,
  "mistral-large-2411": 3,
  "pixtral-large-2411": 2,
  "mistral-large-2407": 2,
  "llama-3.1-nemotron-51b": 2,
  "granite-3.1-8b": 2,
  "granite-3.1-2b": 2,
  "step-2-16k-exp-202412": 2,
  "yi-lightning": 2,
  "glm-4-plus": 2,
  "glm-4-plus-0111": 2,
  "jamba-1.5-large": 2,
  "jamba-1.5-mini": 2,
  "gemma-2-27b-it": 3,
  "gemma-2-9b-it": 3,
  "gemma-2-2b-it": 2,
  "claude-3-haiku": 3,
  "claude-3-sonnet": 3,
  "claude-3-opus": 3,
  "nemotron-4-340b": 2,
  "qwen2.5-plus-1127": 2,
  "qwen2.5-coder-32b": 3,
  "qwen2.5-72b": 3,
  "qwen-max-0919": 2,
  "llama-3.1-tulu-3-70b": 2,
  "gpt-3.5-turbo-0125": 2,
  "reka-core": 2,
  "reka-flash": 3,
  "aya-expanse-32b": 2,
  "aya-expanse-8b": 2,
  "command-r-plus24": 4,
  "command-r24": 3,
  "mixtral-8x22b-instruct-v0.1": 2,
  "mixtral-8x7b-instruct-v0.1": 2,
  "pixtral-12b-2409": 2,
  "ministral-8b-2410": 3,
  "claude-3.7-sonnet": 3,
  "claude-3.7-sonnet-thinking": 3,
  "gemini-2.0-pro": 2,
  "qwen-plus": 3,
  "claude-3.5-sonnet": 3,
  "qwen-2.5-plus": 2,
  "deepseek-v2.5": 2,
  "grok-2-mini": 2,
  "qwen-2.5-vl-32b": 3,
  "gpt-4-turbo": 3,
  "nemotron-49b": 3,
  "mistral-large": 3,
  "mistral-medium": 3,
  "pixtral-large": 3,
  "nemotron-253b": 3,
  "tulu-3-70b": 2,
  "claude-3.5-haiku": 3,
  "gemma-3-4b": 4,
  "deepseek-v2": 2,
  "gemma-2-9b": 4,
  "deepseek-coder-v2": 2,
  "nemotron-51b": 2,
  "glm-4": 3,
  "tulu-3-8b": 2,
  "codestral": 3,
  "qwen-1.5-110b": 2,
  "qwen-1.5-72b": 2,
  "gemma-2-2b": 2,
  "qwen-vl-max": 3,
  "qwen-1.5-32b": 2,
  "qwen-1.5-14b": 3,
  "qwen-1.5-4b": 2,
  "mistral-next": 2,
  "phi-3-medium": 3,
  "phi-3-small": 2,
  "phi-3-mini": 3,
  "tulu-2-70b": 2,
  "llama-2-13b": 3,
  "pplx-7b-online": 2,
  "deepseek-67b": 2,
  "openhermes-2.5-7b": 2,
  "codellama-34b": 2,
  "codellama-70b": 2,
  "qwen-14b": 2,
  "gpt-3.5-turbo": 3,
  "dbrx-instruct": 2,
  "llama-3.3-70b-instruct": 4,
  "deepseek": 6,
  "llama-4-maverick-17b-128e-instruct": 5,
  "llama-4-scout-17b-16e": 5,
  "qwen-332b": 3,
  "hermes-3-405b": 2,
  "qwen-3235b-a22b": 5,
  "qwen-330b-a3b": 3,
  "llama-3.3-70b-instruct-turbo": 2,
  "qwen-272b": 2,
  "mistral-7b-instruct-v0.3": 2,
  "llama-3.1-nemotron-70b-instruct": 3,
  "stable-diffusion-3.5-large": 2,
  "sdxl-1.0": 2,
  "deepseek-coder": 3,
  "claude-2.1": 3,
  "mistral-7b-instruct-v0.2": 3,
  "nous-hermes-2-mixtral-8x7b-dpo": 2,
  "mistral-medium-2505": 4,
  "claude-2.0": 3,
  "claude-opus-4": 2,
  "claude-sonnet-4": 2,
  "qwen-3-8b": 2,
  "grok": 2,
  "deepseek-chat": 2,
  "gemma-3n-e4b-it": 2,
  "default": 15,
  "devstral-small-2505": 2,
  "magistral-small-2506": 2,
  "phi-3-mini-4k": 2,
  "gpt-4-0125": 2,
  "reka-flash-21b": 2,
  "qwen2.5-max": 2,
  "yi-1.5-34b": 2,
  "qwen-max-0428": 2,
  "qwen1.5-110b": 2,
  "gemma-1.1-7b-it": 3,
  "oasst-pythia-12b": 2,
  "dolly-v2-12b": 2,
  "fastchat-t5-3b": 2,
  "wizardlm-70b": 2,
  "olmo-7b": 2,
  "gemma-7b-it": 2,
  "gemma-2-9b-it-simpo": 2,
  "wizardlm-13b": 2,
  "openhermes-2.5-mistral-7b": 3,
  "llama-3.1-tulu-3-8b": 2,
  "llama2-70b-steerlm": 2,
  "deepseek-coder-v2-0724": 2,
  "gemini-pro": 2,
  "openchat-3.5": 2,
  "chatglm3-6b": 2,
  "alpaca-13b": 2,
  "gpt4all-13b-snoozy": 2,
  "qwen1.5-7b": 2,
  "vicuna-13b": 2,
  "snowflake-arctic": 2,
  "deepseek-llm-67b": 2,
  "granite-3.0-2b": 2,
  "solar-10.7b-instruct-v1.0": 2,
  "gemini-exp-1206": 2,
  "granite-3.0-8b": 2,
  "gemini-pro-dev-api": 2,
  "bard-jan-24-gemini-pro": 2,
  "llama-13b": 2,
  "dolphin-2.2.1-mistral-7b": 2,
  "yi-34b": 2,
  "gemini-1.5-pro-exp-0801": 2,
  "claude-1": 2,
  "stablelm-tuned-alpha-7b": 2,
  "starling-lm-7b-beta": 3,
  "gpt-3.5-turbo-0314": 2,
  "smollm2-1.7b": 2,
  "stripedhyena-nous-7b": 2,
  "gemma-2b-it": 2,
  "hunyuan-standard-256k": 2,
  "gpt-4-1106": 2,
  "gemini-1.5-pro-api-0409": 2,
  "yi-lightning-lite": 2,
  "gpt-4-0314": 2,
  "tulu-2-dpo-70b": 2,
  "vicuna-33b": 2,
  "gemini-2.0-pro-exp": 2,
  "gpt-3.5-turbo-1106": 2,
  "deepseek-v2.5-1210": 2,
  "athene-v2": 2,
  "koala-13b": 2,
  "phi-3-small-8k": 2,
  "gemini-2.0-flash-thinking-exp-1219": 2,
  "rwkv-4-raven-14b": 2,
  "zephyr-7b-beta": 3,
  "athene-70b-0725": 2,
  "qwen1.5-14b": 2,
  "palm-2": 2,
  "glm-4-0520": 2,
  "zephyr-7b-alpha": 2,
  "openchat-3.5-0106": 3,
  "falcon-180b": 2,
  "qwen1.5-32b": 2,
  "gemini-1.5-flash-exp-0827": 2,
  "mistral-large-2402": 2,
  "qwen-plus-0828": 2,
  "gemma-1.1-2b-it": 2,
  "guanaco-33b": 2,
  "gemini-1.5-flash-8b-exp-0827": 2,
  "chatglm-6b": 2,
  "gpt-3.5-turbo-0613": 2,
  "vicuna-7b": 2,
  "phi-3-mini-128k": 2,
  "mpt-7b": 2,
  "glm-4-0116": 2,
  "qwen-plus-0125": 2,
  "gemini-1.5-pro-exp-0827": 2,
  "pplx-70b-online": 2,
  "gpt-4-0613": 2,
  "phi-3-mini-4k-instruct-june-2024": 2,
  "early-grok-3": 2,
  "gemini-exp-1114": 2,
  "starling-lm-7b-alpha": 2,
  "yi-large": 2,
  "qwen1.5-4b": 2,
  "internlm2.5-20b": 2,
  "qwen1.5-72b": 2,
  "claude-instant-1": 2,
  "deepseek-v2-api-0628": 2,
  "zephyr-orpo-141b-a35b-v0.1": 2,
  "mpt-30b": 2,
  "chatglm2-6b": 2,
  "gemini-advanced-0514": 2,
  "phi-3-medium-4k": 2,
  "reka-flash-21b-********-online": 2,
  "gemini-exp-1121": 2,
  "flux-kontext-dev": 2,
  "llama-3.1-70b-instruct-turbo": 2,
  "kimi-k2": 2,
  "devstral-small-2507": 2,
  "llama-3.2-11b-vision": 2,
  "claude-3-7-sonnet-latest": 2,
  "claude-3-5-sonnet-latest": 2,
  "moz-llama-3-3-70b-instruct-turbo": 2,
  "claude-2": 2,
  "command": 2,
  "grok-3-mini-high": 2,
  "mistral-small-3.1-24b-instruct-2503": 2,
  "magistral-medium-2506": 2,
  "mistral-small-2506": 2
}
parents = {
  "Copilot": [
    "CopilotAccount"
  ],
  "DeepInfra": [
    "DeepInfraChat"
  ],
  "HuggingFace": [
    "HuggingFaceMedia"
  ]
}
