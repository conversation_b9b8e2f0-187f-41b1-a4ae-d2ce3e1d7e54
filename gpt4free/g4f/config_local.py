"""
Local-only configuration for GPT4Free
完全本地化配置，禁用所有网络功能
"""

import os
import logging

# 设置环境变量强制使用本地模式
os.environ['G4F_LOCAL_ONLY'] = 'true'
os.environ['G4F_DISABLE_NETWORK'] = 'true'
os.environ['G4F_DISABLE_UPDATES'] = 'true'
os.environ['G4F_DISABLE_ANALYTICS'] = 'true'

# Ollama 配置
os.environ['OLLAMA_HOST'] = 'localhost'
os.environ['OLLAMA_PORT'] = '11434'

# 禁用版本检查
os.environ['G4F_DISABLE_VERSION_CHECK'] = 'true'

# 配置日志级别
logging.getLogger().setLevel(logging.WARNING)

# 禁用的功能列表
DISABLED_FEATURES = [
    'version_check',
    'analytics',
    'telemetry', 
    'update_check',
    'online_search',
    'image_generation_online',
    'remote_providers'
]

# 允许的本地 Provider 列表
ALLOWED_PROVIDERS = [
    'Ollama',
    'Local'
]

# 禁用的网络相关 Provider
DISABLED_PROVIDERS = [
    'Blackbox', 'Chatai', 'Cloudflare', 'Copilot', 'DeepInfraChat',
    'Free2GPT', 'HuggingSpace', 'Grok', 'DeepseekAI_JanusPro7b',
    'DeepSeekAPI', 'ImageLabs', 'LambdaChat', 'OIVSCodeSer2',
    'OIVSCodeSer0501', 'OperaAria', 'Startnest', 'OpenAIFM',
    'PerplexityLabs', 'PollinationsAI', 'PollinationsImage',
    'TeachAnything', 'Together', 'WeWordle', 'Yqcloud',
    'BingCreateImages', 'CopilotAccount', 'Gemini', 'GeminiPro',
    'HuggingChat', 'HuggingFace', 'HuggingFaceMedia', 'HuggingFaceAPI',
    'MetaAI', 'MicrosoftDesigner', 'OpenaiAccount', 'OpenaiChat'
]

def is_local_mode():
    """检查是否为本地模式"""
    return os.environ.get('G4F_LOCAL_ONLY', 'false').lower() == 'true'

def is_network_disabled():
    """检查是否禁用网络"""
    return os.environ.get('G4F_DISABLE_NETWORK', 'false').lower() == 'true'

def get_ollama_base_url():
    """获取 Ollama 服务地址"""
    host = os.environ.get('OLLAMA_HOST', 'localhost')
    port = os.environ.get('OLLAMA_PORT', '11434')
    return f"http://{host}:{port}"

print("🔒 Local-only mode enabled for GPT4Free")
print(f"📍 Ollama service: {get_ollama_base_url()}")
print("🚫 All network features disabled")
