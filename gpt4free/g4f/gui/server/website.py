from __future__ import annotations

import os
import requests
from datetime import datetime
from flask import send_from_directory, redirect, request

from ...image.copy_images import secure_filename
from ...cookies import get_cookies_dir
from ...errors import VersionNotFoundError
from ...config import STATIC_URL, DOWNLOAD_URL, DIST_DIR
from ... import version

def redirect_home():
    return redirect('/chat/')

def render_local_page(filename="home"):
    """渲染本地定制页面，去掉版本信息和链接"""
    from flask import Response
    print(f"[DEBUG] render_local_page 被调用，文件名: {filename}")
    
    # 定制的本地 HTML 模板
    local_html = f"""
<!DOCTYPE html>
<html lang="en" data-color-mode="auto" data-light-theme="light" data-dark-theme="dark" class="color-mode-auto">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPT4Free - Local Mode</title>
    <link rel="apple-touch-icon" sizes="180x180" href="/dist/img/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/dist/img/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/dist/img/favicon-16x16.png">
    <link rel="manifest" href="/dist/img/site.webmanifest">
    
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow: hidden;
            height: 100vh;
            position: relative;
        }}
        
        .container {{
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            text-align: center;
            position: relative;
            z-index: 1;
        }}
        
        .logo {{
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .subtitle {{
            font-size: 1.5rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }}
        
        .features {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
            max-width: 800px;
            padding: 0 2rem;
        }}
        
        .feature {{
            background: rgba(255,255,255,0.1);
            padding: 1.5rem;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }}
        
        .feature h3 {{
            margin-bottom: 0.5rem;
            color: #fff;
        }}
        
        .feature p {{
            opacity: 0.8;
            font-size: 0.9rem;
        }}
        
        .start-btn {{
            margin-top: 2rem;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }}
        
        .start-btn:hover {{
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }}
        
        .background-animation {{
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }}
        
        .floating {{
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }}
        
        @keyframes float {{
            0%, 100% {{ transform: translateY(0px); }}
            50% {{ transform: translateY(-20px); }}
        }}
        
        .chat-interface {{
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #1a1a1a;
            z-index: 1000;
        }}
    </style>
</head>
<body>
    <div class="background-animation">
        <div class="floating" style="top: 20%; left: 10%; width: 60px; height: 60px; animation-delay: 0s;"></div>
        <div class="floating" style="top: 60%; left: 80%; width: 80px; height: 80px; animation-delay: 2s;"></div>
        <div class="floating" style="top: 80%; left: 20%; width: 40px; height: 40px; animation-delay: 4s;"></div>
        <div class="floating" style="top: 30%; left: 70%; width: 50px; height: 50px; animation-delay: 1s;"></div>
    </div>
    
    <div class="container">
        <h1 class="logo">🏠 GPT4Free</h1>
        <p class="subtitle">本地模式 - 完全私密的AI聊天</p>
        
        <div class="features">
            <div class="feature">
                <h3>🔒 完全私密</h3>
                <p>所有对话都在本地处理，无需联网，保护您的隐私</p>
            </div>
            <div class="feature">
                <h3>🚀 高性能</h3>
                <p>使用本地Ollama模型，响应迅速，无网络延迟</p>
            </div>
            <div class="feature">
                <h3>🎯 多模态</h3>
                <p>支持文本、代码、图像等多种AI模型类型</p>
            </div>
        </div>
        
        <a href="/chat/" class="start-btn">开始聊天 →</a>
    </div>
    
    <script>
        // 自动跳转到聊天界面
        setTimeout(() => {{
            window.location.href = '/chat/';
        }}, 3000);
        
        // 版本信息隐藏
        window.hideVersion = true;
        window.hideSupportLinks = true;
        
        // 隐藏版本和支持链接的CSS
        const style = document.createElement('style');
        style.textContent = `
            .version-info,
            .support-links,
            [data-version],
            [href*="discord"],
            [href*="github.com"],
            .footer-links,
            .social-links {{
                display: none !important;
                visibility: hidden !important;
            }}
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
    """
    
    if filename == "chat/index" or filename == "chat/index.html":
        # 如果是聊天页面，返回定制的聊天界面
        return render_local_chat_page()
    
    return Response(local_html, mimetype='text/html')

def render_local_chat_page():
    """渲染增强版的本地聊天页面"""
    from flask import Response

    chat_html = """
<!DOCTYPE html>
<html lang="en" data-color-mode="auto" data-light-theme="light" data-dark-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPT4Free - 本地聊天</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', system-ui, sans-serif;
            background: #0d1117;
            color: #f0f6fc;
            height: 100vh;
            display: flex;
            overflow: hidden;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 280px;
            background: #161b22;
            border-right: 1px solid #30363d;
            display: flex;
            flex-direction: column;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 1rem;
            border-bottom: 1px solid #30363d;
        }

        .logo {
            font-size: 1.2rem;
            font-weight: 600;
            color: #58a6ff;
            margin-bottom: 1rem;
        }

        .new-chat-btn {
            width: 100%;
            background: #238636;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.75rem;
            font-size: 0.9rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .new-chat-btn:hover {
            background: #2ea043;
        }

        .private-chat-btn {
            width: 100%;
            background: #21262d;
            color: #f0f6fc;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 0.75rem;
            font-size: 0.9rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .private-chat-btn:hover {
            background: #30363d;
        }

        .sidebar-content {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
        }

        .conversation-item {
            padding: 0.75rem;
            border-radius: 6px;
            margin-bottom: 0.5rem;
            cursor: pointer;
            border: 1px solid transparent;
        }

        .conversation-item:hover {
            background: #21262d;
            border-color: #30363d;
        }

        .conversation-item.active {
            background: #1f6feb;
            color: white;
        }

        .sidebar-footer {
            padding: 1rem;
            border-top: 1px solid #30363d;
        }

        .settings-btn {
            width: 100%;
            background: #21262d;
            color: #f0f6fc;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 0.75rem;
            font-size: 0.9rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .settings-btn:hover {
            background: #30363d;
        }

        /* 主聊天区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            background: #161b22;
            padding: 1rem;
            border-bottom: 1px solid #30363d;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-title {
            font-size: 1.1rem;
            font-weight: 600;
        }

        .header-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .toggle-sidebar-btn {
            background: #21262d;
            color: #f0f6fc;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 0.5rem;
            cursor: pointer;
            display: none;
        }

        .provider-select, .model-select {
            background: #21262d;
            color: #f0f6fc;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 0.5rem;
            min-width: 120px;
        }

        /* 系统提示区域 */
        .system-prompt-area {
            background: #161b22;
            border-bottom: 1px solid #30363d;
            padding: 1rem;
        }

        .system-prompt-input {
            width: 100%;
            background: #21262d;
            color: #f0f6fc;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 0.75rem;
            font-size: 0.9rem;
            resize: vertical;
            min-height: 40px;
        }

        .system-prompt-input::placeholder {
            color: #7d8590;
        }

        /* 聊天消息区域 */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 2rem 1rem;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
        }

        .message {
            margin-bottom: 1.5rem;
            display: flex;
            flex-direction: column;
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.85rem;
            color: #7d8590;
        }

        .message-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
        }

        .user-avatar {
            background: #1f6feb;
            color: white;
        }

        .bot-avatar {
            background: #238636;
            color: white;
        }

        .message-content {
            padding: 1rem;
            border-radius: 8px;
            position: relative;
            max-width: 80%;
        }

        .user-message .message-content {
            background: #1f6feb;
            margin-left: auto;
            color: white;
        }

        .bot-message .message-content {
            background: #21262d;
            border: 1px solid #30363d;
        }

        .message-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .message:hover .message-actions {
            opacity: 1;
        }

        .action-btn {
            background: #21262d;
            color: #7d8590;
            border: 1px solid #30363d;
            border-radius: 4px;
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
            cursor: pointer;
        }

        .action-btn:hover {
            background: #30363d;
            color: #f0f6fc;
        }

        /* 输入区域样式 */
        .input-area {
            background: #161b22;
            border-top: 1px solid #30363d;
            padding: 1rem;
        }

        .input-container {
            max-width: 800px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .input-row {
            display: flex;
            gap: 0.5rem;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            background: #21262d;
            color: #f0f6fc;
            border: 1px solid #30363d;
            border-radius: 8px;
            padding: 0.75rem;
            font-size: 0.9rem;
            resize: vertical;
            min-height: 44px;
            max-height: 200px;
            font-family: inherit;
        }

        .message-input::placeholder {
            color: #7d8590;
        }

        .message-input:focus {
            outline: none;
            border-color: #1f6feb;
            box-shadow: 0 0 0 2px rgba(31, 111, 235, 0.2);
        }

        .input-actions {
            display: flex;
            gap: 0.5rem;
        }

        .file-upload-btn, .send-button {
            background: #238636;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.75rem 1rem;
            font-size: 0.9rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            white-space: nowrap;
        }

        .file-upload-btn {
            background: #6f42c1;
        }

        .file-upload-btn:hover {
            background: #8a63d2;
        }

        .send-button:hover {
            background: #2ea043;
        }

        .send-button:disabled {
            background: #30363d;
            color: #7d8590;
            cursor: not-allowed;
        }

        .model-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .control-label {
            font-size: 0.85rem;
            color: #7d8590;
            white-space: nowrap;
        }

        /* 文件上传区域 */
        .file-drop-zone {
            border: 2px dashed #30363d;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            color: #7d8590;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            display: none;
        }

        .file-drop-zone.active {
            display: block;
            border-color: #1f6feb;
            background: rgba(31, 111, 235, 0.1);
        }

        .file-drop-zone.dragover {
            border-color: #238636;
            background: rgba(35, 134, 54, 0.1);
        }

        /* 欢迎消息样式 */
        .welcome {
            text-align: center;
            padding: 3rem 2rem;
            color: #7d8590;
        }

        .welcome h2 {
            color: #f0f6fc;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .welcome p {
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: 0;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transform: translateX(-100%);
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .toggle-sidebar-btn {
                display: block;
            }

            .header-controls {
                flex-wrap: wrap;
                gap: 0.5rem;
            }

            .model-controls {
                flex-direction: column;
                align-items: stretch;
                gap: 0.5rem;
            }

            .input-row {
                flex-direction: column;
                gap: 0.5rem;
            }

            .input-actions {
                justify-content: stretch;
            }

            .file-upload-btn, .send-button {
                flex: 1;
                justify-content: center;
            }
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #30363d;
            border-radius: 50%;
            border-top-color: #1f6feb;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 滚动条样式 */
        .messages::-webkit-scrollbar,
        .sidebar-content::-webkit-scrollbar {
            width: 8px;
        }

        .messages::-webkit-scrollbar-track,
        .sidebar-content::-webkit-scrollbar-track {
            background: #161b22;
        }

        .messages::-webkit-scrollbar-thumb,
        .sidebar-content::-webkit-scrollbar-thumb {
            background: #30363d;
            border-radius: 4px;
        }

        .messages::-webkit-scrollbar-thumb:hover,
        .sidebar-content::-webkit-scrollbar-thumb:hover {
            background: #484f58;
        }
        
        .message-input {
            flex: 1;
            background: #0d1117;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 0.75rem;
            color: #f0f6fc;
            resize: none;
            min-height: 44px;
            max-height: 120px;
        }
        
        .send-button {
            background: #238636;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.75rem 1.5rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .send-button:hover {
            background: #2ea043;
        }
        
        .send-button:disabled {
            background: #484f58;
            cursor: not-allowed;
        }
        
        .welcome {
            text-align: center;
            padding: 3rem 1rem;
            color: #7d8590;
        }
        
        .welcome h2 {
            color: #f0f6fc;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">🏠 GPT4Free - 本地模式</div>
            <button class="new-chat-btn" id="newChatBtn">
                <span>+</span>
                <span>新建对话</span>
            </button>
            <button class="private-chat-btn" id="privateChatBtn">
                <span>🔒</span>
                <span>私密对话</span>
            </button>
        </div>

        <div class="sidebar-content" id="conversationList">
            <div class="conversation-item active">
                <div>当前对话</div>
                <div style="font-size: 0.8rem; color: #7d8590; margin-top: 0.25rem;">本地Ollama聊天</div>
            </div>
        </div>

        <div class="sidebar-footer">
            <button class="settings-btn" id="settingsBtn">
                <span>⚙️</span>
                <span>设置</span>
            </button>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 聊天头部 -->
        <div class="chat-header">
            <div class="chat-title">
                <button class="toggle-sidebar-btn" id="toggleSidebar">☰</button>
                本地AI聊天
            </div>
            <div class="header-controls">
                <div class="control-group">
                    <span class="control-label">Provider:</span>
                    <select class="provider-select" id="provider">
                        <option value="Ollama">Ollama</option>
                    </select>
                </div>
                <div class="control-group">
                    <span class="control-label">Model:</span>
                    <select class="model-select" id="model">
                        <option value="">加载中...</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 系统提示区域 -->
        <div class="system-prompt-area">
            <textarea
                class="system-prompt-input"
                id="systemPrompt"
                placeholder="系统提示 (可选) - 设置AI的角色和行为..."
                rows="2"
            ></textarea>
        </div>

        <!-- 聊天容器 -->
        <div class="chat-container">
            <!-- 文件拖拽区域 -->
            <div class="file-drop-zone" id="fileDropZone">
                <div>📁 拖拽文件到这里上传</div>
                <div style="font-size: 0.8rem; margin-top: 0.5rem;">支持图片、文档等多种格式</div>
            </div>

            <!-- 消息区域 -->
            <div class="messages" id="messages">
                <div class="welcome">
                    <h2>欢迎使用本地AI聊天</h2>
                    <p>🔒 您的对话完全私密，所有处理都在本地完成</p>
                    <p>🚀 选择一个模型开始对话吧！</p>
                    <p>💡 您可以设置系统提示来定制AI的行为</p>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="input-area">
                <div class="input-container">
                    <!-- 模型控制 -->
                    <div class="model-controls">
                        <div class="control-group">
                            <span class="control-label">温度:</span>
                            <input type="range" id="temperature" min="0" max="2" step="0.1" value="0.7" style="width: 80px;">
                            <span id="temperatureValue">0.7</span>
                        </div>
                        <div class="control-group">
                            <span class="control-label">最大长度:</span>
                            <input type="number" id="maxTokens" min="1" max="4096" value="2048" style="width: 80px; background: #21262d; color: #f0f6fc; border: 1px solid #30363d; border-radius: 4px; padding: 0.25rem;">
                        </div>
                        <div class="control-group">
                            <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                <input type="checkbox" id="streamMode" checked style="accent-color: #1f6feb;">
                                <span class="control-label">流式输出</span>
                            </label>
                        </div>
                    </div>

                    <!-- 输入行 -->
                    <div class="input-row">
                        <textarea
                            class="message-input"
                            id="messageInput"
                            placeholder="输入您的消息... (Shift+Enter 换行，Enter 发送)"
                            rows="1"
                        ></textarea>
                        <div class="input-actions">
                            <input type="file" id="fileInput" multiple accept="image/*,text/*,.pdf,.doc,.docx" style="display: none;">
                            <button class="file-upload-btn" id="fileUploadBtn">
                                <span>📎</span>
                                <span>文件</span>
                            </button>
                            <button class="send-button" id="sendButton">
                                <span>📤</span>
                                <span>发送</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 隐藏版本信息和支持链接
        const hideStyle = document.createElement('style');
        hideStyle.textContent = `
            .version-info, .support-links, [data-version],
            [href*="discord"], [href*="github.com"],
            .footer-links, .social-links, .support-text {
                display: none !important;
                visibility: hidden !important;
            }
        `;
        document.head.appendChild(hideStyle);

        // 全局变量
        let isLoading = false;
        let conversations = [];
        let currentConversationId = 'default';
        let messageHistory = [];
        let uploadedFiles = [];

        // 本地存储键名
        const STORAGE_KEYS = {
            conversations: 'gpt4free_conversations',
            currentConversationId: 'gpt4free_current_conversation',
            settings: 'gpt4free_settings'
        };

        // 保存数据到本地存储
        function saveToStorage() {
            try {
                localStorage.setItem(STORAGE_KEYS.conversations, JSON.stringify(conversations));
                localStorage.setItem(STORAGE_KEYS.currentConversationId, currentConversationId);
            } catch (error) {
                console.warn('无法保存到本地存储:', error);
            }
        }

        // 从本地存储加载数据
        function loadFromStorage() {
            try {
                const savedConversations = localStorage.getItem(STORAGE_KEYS.conversations);
                const savedCurrentId = localStorage.getItem(STORAGE_KEYS.currentConversationId);

                if (savedConversations) {
                    conversations = JSON.parse(savedConversations);
                }

                if (savedCurrentId) {
                    currentConversationId = savedCurrentId;
                    const currentConv = conversations.find(c => c.id === currentConversationId);
                    if (currentConv) {
                        messageHistory = currentConv.messages || [];
                    }
                }

                // 如果没有对话，创建默认对话
                if (conversations.length === 0) {
                    conversations.push({
                        id: 'default',
                        title: '默认对话',
                        messages: [],
                        createdAt: new Date()
                    });
                    currentConversationId = 'default';
                }
            } catch (error) {
                console.warn('无法从本地存储加载数据:', error);
                // 创建默认对话
                conversations = [{
                    id: 'default',
                    title: '默认对话',
                    messages: [],
                    createdAt: new Date()
                }];
                currentConversationId = 'default';
            }
        }
        
        // 侧边栏控制
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }

        // 新建对话
        function newConversation() {
            const conversationId = 'conv_' + Date.now();
            conversations.push({
                id: conversationId,
                title: '新对话',
                messages: [],
                createdAt: new Date()
            });

            currentConversationId = conversationId;
            messageHistory = [];

            // 清空消息区域
            const messagesContainer = document.getElementById('messages');
            messagesContainer.innerHTML = `
                <div class="welcome">
                    <h2>新对话已创建</h2>
                    <p>🔒 您的对话完全私密，所有处理都在本地完成</p>
                    <p>🚀 开始您的新对话吧！</p>
                </div>
            `;

            updateConversationList();
            saveToStorage(); // 保存到本地存储
        }

        // 更新对话列表
        function updateConversationList() {
            const listContainer = document.getElementById('conversationList');
            listContainer.innerHTML = '';

            conversations.forEach(conv => {
                const item = document.createElement('div');
                item.className = `conversation-item ${conv.id === currentConversationId ? 'active' : ''}`;
                item.innerHTML = `
                    <div>${conv.title}</div>
                    <div style="font-size: 0.8rem; color: #7d8590; margin-top: 0.25rem;">
                        ${conv.messages.length} 条消息
                    </div>
                `;
                item.onclick = () => switchConversation(conv.id);
                listContainer.appendChild(item);
            });

            // 如果没有对话，显示默认项
            if (conversations.length === 0) {
                listContainer.innerHTML = `
                    <div class="conversation-item active">
                        <div>当前对话</div>
                        <div style="font-size: 0.8rem; color: #7d8590; margin-top: 0.25rem;">本地Ollama聊天</div>
                    </div>
                `;
            }
        }

        // 切换对话
        function switchConversation(conversationId) {
            currentConversationId = conversationId;
            const conversation = conversations.find(c => c.id === conversationId);

            if (conversation) {
                messageHistory = conversation.messages;
                renderMessages();
            }

            updateConversationList();
        }

        // 渲染消息
        function renderMessages() {
            const messagesContainer = document.getElementById('messages');
            messagesContainer.innerHTML = '';

            if (messageHistory.length === 0) {
                messagesContainer.innerHTML = `
                    <div class="welcome">
                        <h2>欢迎使用本地AI聊天</h2>
                        <p>🔒 您的对话完全私密，所有处理都在本地完成</p>
                        <p>🚀 选择一个模型开始对话吧！</p>
                        <p>💡 您可以设置系统提示来定制AI的行为</p>
                    </div>
                `;
                return;
            }

            messageHistory.forEach(msg => {
                addMessageToDOM(msg.content, msg.role, false);
            });
        }

        // 加载模型列表
        async function loadModels() {
            try {
                const response = await fetch('/backend-api/v2/models');
                const data = await response.json();
                const modelSelect = document.getElementById('model');
                modelSelect.innerHTML = '';

                data.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.name;
                    option.textContent = model.name;
                    modelSelect.appendChild(option);
                });

                if (data.length > 0) {
                    modelSelect.value = data[0].name;
                }
            } catch (error) {
                console.error('Failed to load models:', error);
                document.getElementById('model').innerHTML = '<option value="">模型加载失败</option>';
            }
        }
        
        // 文件上传处理
        function handleFileUpload() {
            const fileInput = document.getElementById('fileInput');
            fileInput.click();
        }

        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            files.forEach(file => {
                if (file.size > 10 * 1024 * 1024) { // 10MB limit
                    alert(`文件 ${file.name} 太大，请选择小于10MB的文件`);
                    return;
                }

                // 读取文件内容
                const reader = new FileReader();
                reader.onload = function(e) {
                    const fileData = {
                        name: file.name,
                        type: file.type,
                        size: file.size,
                        content: e.target.result,
                        isImage: file.type.startsWith('image/')
                    };

                    uploadedFiles.push(fileData);
                    showUploadedFiles();
                };

                // 根据文件类型选择读取方式
                if (file.type.startsWith('image/')) {
                    reader.readAsDataURL(file);
                } else {
                    reader.readAsText(file);
                }
            });
        }

        function showUploadedFiles() {
            if (uploadedFiles.length === 0) {
                clearFileDisplay();
                return;
            }

            // 创建或更新文件显示区域
            let fileDisplay = document.getElementById('fileDisplay');
            if (!fileDisplay) {
                fileDisplay = document.createElement('div');
                fileDisplay.id = 'fileDisplay';
                fileDisplay.style.cssText = `
                    background: #21262d;
                    border: 1px solid #30363d;
                    border-radius: 6px;
                    padding: 0.75rem;
                    margin-bottom: 0.5rem;
                    display: flex;
                    flex-wrap: wrap;
                    gap: 0.5rem;
                    align-items: center;
                `;

                const inputContainer = document.querySelector('.input-container');
                const inputRow = inputContainer.querySelector('.input-row');
                inputContainer.insertBefore(fileDisplay, inputRow);
            }

            fileDisplay.innerHTML = '';

            uploadedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.style.cssText = `
                    background: #161b22;
                    border: 1px solid #30363d;
                    border-radius: 4px;
                    padding: 0.5rem;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 0.85rem;
                    max-width: 200px;
                `;

                const fileIcon = file.isImage ? '🖼️' : '📄';
                const fileName = file.name.length > 15 ? file.name.substring(0, 15) + '...' : file.name;
                const fileSize = (file.size / 1024).toFixed(1) + 'KB';

                fileItem.innerHTML = `
                    <span>${fileIcon}</span>
                    <div style="flex: 1; min-width: 0;">
                        <div style="color: #f0f6fc; font-weight: 500; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">${fileName}</div>
                        <div style="color: #7d8590; font-size: 0.75rem;">${fileSize}</div>
                    </div>
                    <button onclick="removeFile(${index})" style="
                        background: none;
                        border: none;
                        color: #f85149;
                        cursor: pointer;
                        padding: 0.25rem;
                        border-radius: 3px;
                        font-size: 0.8rem;
                    " title="删除文件">✕</button>
                `;

                fileDisplay.appendChild(fileItem);
            });

            // 添加清除所有文件的按钮
            if (uploadedFiles.length > 1) {
                const clearAllBtn = document.createElement('button');
                clearAllBtn.textContent = '清除所有';
                clearAllBtn.style.cssText = `
                    background: #f85149;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 0.5rem 0.75rem;
                    font-size: 0.8rem;
                    cursor: pointer;
                    margin-left: auto;
                `;
                clearAllBtn.onclick = clearAllFiles;
                fileDisplay.appendChild(clearAllBtn);
            }
        }

        function removeFile(index) {
            uploadedFiles.splice(index, 1);
            showUploadedFiles();
        }

        function clearAllFiles() {
            uploadedFiles = [];
            clearFileDisplay();
        }

        function clearFileDisplay() {
            const fileDisplay = document.getElementById('fileDisplay');
            if (fileDisplay) {
                fileDisplay.remove();
            }
            const input = document.getElementById('messageInput');
            input.placeholder = '输入您的消息... (Shift+Enter 换行，Enter 发送)';
        }

        // 拖拽文件处理
        function setupFileDrop() {
            const dropZone = document.getElementById('fileDropZone');
            const chatContainer = document.querySelector('.chat-container');

            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                chatContainer.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                chatContainer.addEventListener(eventName, () => {
                    dropZone.classList.add('active', 'dragover');
                }, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                chatContainer.addEventListener(eventName, () => {
                    dropZone.classList.remove('active', 'dragover');
                }, false);
            });

            chatContainer.addEventListener('drop', handleDrop, false);

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                handleFileSelect({ target: { files: files } });
            }
        }

        // 发送消息
        async function sendMessage() {
            if (isLoading) return;

            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            const systemPrompt = document.getElementById('systemPrompt').value.trim();

            if (!message && uploadedFiles.length === 0) return;

            const selectedModel = document.getElementById('model').value;
            if (!selectedModel) {
                alert('请先选择一个模型');
                return;
            }

            // 构建消息历史
            let messages = [];

            // 添加系统提示
            if (systemPrompt) {
                messages.push({ role: 'system', content: systemPrompt });
            }

            // 添加历史消息
            messages.push(...messageHistory);

            // 构建用户消息内容
            let userContent = message;

            // 如果有上传的文件，添加文件信息到消息中
            if (uploadedFiles.length > 0) {
                userContent += '\\n\\n📎 附件：\\n';
                uploadedFiles.forEach(file => {
                    if (file.isImage) {
                        userContent += `🖼️ 图片: ${file.name}\\n`;
                        // 对于图片，可以添加base64数据（如果后端支持）
                        // userContent += `数据: ${file.content}\\n`;
                    } else {
                        userContent += `📄 文件: ${file.name}\\n`;
                        userContent += `内容: ${file.content.substring(0, 500)}${file.content.length > 500 ? '...' : ''}\\n`;
                    }
                });
            }

            // 添加当前用户消息
            const userMessage = { role: 'user', content: userContent };
            messages.push(userMessage);
            messageHistory.push(userMessage);

            // 显示用户消息（显示原始消息+文件信息）
            let displayContent = message;
            if (uploadedFiles.length > 0) {
                displayContent += `\\n\\n📎 已上传 ${uploadedFiles.length} 个文件: ${uploadedFiles.map(f => f.name).join(', ')}`;
            }
            addMessageToDOM(displayContent, 'user', true);

            // 清理输入
            input.value = '';
            clearFileDisplay();

            // 发送到后端
            isLoading = true;
            const sendButton = document.getElementById('sendButton');
            const originalContent = sendButton.innerHTML;
            sendButton.innerHTML = '<span class="loading"></span><span>发送中...</span>';
            sendButton.disabled = true;

            try {
                const streamMode = document.getElementById('streamMode').checked;
                const temperature = parseFloat(document.getElementById('temperature').value);
                const maxTokens = parseInt(document.getElementById('maxTokens').value);

                const response = await fetch('/backend-api/v2/conversation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model: selectedModel,
                        provider: 'Ollama',
                        messages: messages,
                        stream: streamMode,
                        temperature: temperature,
                        max_tokens: maxTokens
                    })
                });

                if (response.ok) {
                    if (streamMode) {
                        await handleStreamResponse(response);
                    } else {
                        const data = await response.json();
                        if (data.choices && data.choices[0] && data.choices[0].message) {
                            const botMessage = { role: 'assistant', content: data.choices[0].message.content };
                            messageHistory.push(botMessage);
                            addMessageToDOM(data.choices[0].message.content, 'assistant', true);
                        } else {
                            addMessageToDOM('抱歉，没有收到有效响应。', 'assistant', true);
                        }
                    }
                } else {
                    addMessageToDOM('请求失败，请检查模型是否正常运行。', 'assistant', true);
                }
            } catch (error) {
                addMessageToDOM('连接失败：' + error.message, 'assistant', true);
            }

            isLoading = false;
            sendButton.innerHTML = originalContent;
            sendButton.disabled = false;

            // 更新对话
            updateCurrentConversation();
        }

        // 处理流式响应
        async function handleStreamResponse(response) {
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let botMessage = '';
            let botMessageElement = null;

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\\n');

                for (const line of lines) {
                    if (line.trim()) {
                        try {
                            const data = JSON.parse(line);
                            if (data.type === 'content' && data.content) {
                                botMessage += data.content;
                                if (!botMessageElement) {
                                    botMessageElement = addMessageToDOM('', 'assistant', false);
                                }
                                botMessageElement.querySelector('.message-content').textContent = botMessage;
                            }
                        } catch (e) {
                            // 忽略解析错误
                        }
                    }
                }
            }

            if (botMessage) {
                const botMessageObj = { role: 'assistant', content: botMessage };
                messageHistory.push(botMessageObj);
            } else {
                addMessageToDOM('抱歉，没有收到有效响应。', 'assistant', true);
            }
        }
        
        // 添加消息到DOM
        function addMessageToDOM(content, role, addToHistory = true) {
            const messagesContainer = document.getElementById('messages');

            // 移除欢迎消息
            const welcome = messagesContainer.querySelector('.welcome');
            if (welcome) {
                welcome.remove();
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}-message`;

            const avatar = role === 'user' ? '👤' : '🤖';
            const roleName = role === 'user' ? '您' : 'AI助手';
            const timestamp = new Date().toLocaleTimeString();

            messageDiv.innerHTML = `
                <div class="message-header">
                    <div class="message-avatar ${role}-avatar">${avatar}</div>
                    <span>${roleName}</span>
                    <span style="margin-left: auto;">${timestamp}</span>
                </div>
                <div class="message-content">${content}</div>
                <div class="message-actions">
                    <button class="action-btn" onclick="copyMessage(this)">复制</button>
                    ${role === 'assistant' ? '<button class="action-btn" onclick="regenerateMessage(this)">重新生成</button>' : ''}
                    <button class="action-btn" onclick="deleteMessage(this)">删除</button>
                </div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            return messageDiv;
        }

        // 兼容旧的addMessage函数
        function addMessage(content, type) {
            return addMessageToDOM(content, type, true);
        }

        // 更新当前对话
        function updateCurrentConversation() {
            const conversation = conversations.find(c => c.id === currentConversationId);
            if (conversation) {
                conversation.messages = [...messageHistory];
                if (messageHistory.length > 0 && (conversation.title === '新对话' || conversation.title === '默认对话')) {
                    // 使用第一条用户消息作为标题
                    const firstUserMessage = messageHistory.find(m => m.role === 'user');
                    if (firstUserMessage) {
                        conversation.title = firstUserMessage.content.substring(0, 20) + (firstUserMessage.content.length > 20 ? '...' : '');
                    }
                }
                updateConversationList();
                saveToStorage(); // 保存到本地存储
            }
        }

        // 消息操作函数
        function copyMessage(button) {
            const messageContent = button.closest('.message').querySelector('.message-content').textContent;
            navigator.clipboard.writeText(messageContent).then(() => {
                button.textContent = '已复制';
                setTimeout(() => {
                    button.textContent = '复制';
                }, 2000);
            });
        }

        function regenerateMessage(button) {
            if (isLoading) return;

            const messageElement = button.closest('.message');
            const messageIndex = Array.from(messageElement.parentNode.children).indexOf(messageElement);

            // 移除当前消息及之后的所有消息
            const messages = messageElement.parentNode.children;
            for (let i = messages.length - 1; i >= messageIndex; i--) {
                if (!messages[i].classList.contains('welcome')) {
                    messages[i].remove();
                }
            }

            // 从历史记录中移除对应的消息
            if (messageIndex > 0) {
                messageHistory = messageHistory.slice(0, messageIndex - 1);
            }

            // 重新发送最后一条用户消息
            if (messageHistory.length > 0) {
                const lastUserMessage = messageHistory[messageHistory.length - 1];
                if (lastUserMessage.role === 'user') {
                    messageHistory.pop(); // 移除最后一条用户消息，sendMessage会重新添加
                    document.getElementById('messageInput').value = lastUserMessage.content;
                    sendMessage();
                }
            }
        }

        function deleteMessage(button) {
            if (confirm('确定要删除这条消息吗？')) {
                const messageElement = button.closest('.message');
                const messageIndex = Array.from(messageElement.parentNode.children).indexOf(messageElement);

                messageElement.remove();

                // 从历史记录中移除对应的消息
                if (messageIndex >= 0 && messageIndex < messageHistory.length) {
                    messageHistory.splice(messageIndex, 1);
                    updateCurrentConversation();
                }

                // 如果没有消息了，显示欢迎界面
                const messagesContainer = document.getElementById('messages');
                if (messagesContainer.children.length === 0) {
                    messagesContainer.innerHTML = `
                        <div class="welcome">
                            <h2>欢迎使用本地AI聊天</h2>
                            <p>🔒 您的对话完全私密，所有处理都在本地完成</p>
                            <p>🚀 选择一个模型开始对话吧！</p>
                            <p>💡 您可以设置系统提示来定制AI的行为</p>
                        </div>
                    `;
                }
            }
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadFromStorage(); // 加载历史数据
            loadModels();
            setupFileDrop();

            // 绑定侧边栏控制
            document.getElementById('toggleSidebar').addEventListener('click', toggleSidebar);
            document.getElementById('newChatBtn').addEventListener('click', newConversation);
            document.getElementById('privateChatBtn').addEventListener('click', () => {
                alert('私密对话功能开发中...');
            });
            document.getElementById('settingsBtn').addEventListener('click', () => {
                alert('设置功能开发中...');
            });

            // 绑定文件上传
            document.getElementById('fileUploadBtn').addEventListener('click', handleFileUpload);
            document.getElementById('fileInput').addEventListener('change', handleFileSelect);

            // 绑定发送按钮事件
            document.getElementById('sendButton').addEventListener('click', sendMessage);

            // 绑定输入框事件
            const messageInput = document.getElementById('messageInput');
            messageInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                } else if (e.key === 'Enter' && e.shiftKey) {
                    // Shift+Enter 换行，不做任何处理，让默认行为发生
                }
            });

            // 自动调整输入框高度
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 200) + 'px';
            });

            // 绑定温度滑块
            const temperatureSlider = document.getElementById('temperature');
            const temperatureValue = document.getElementById('temperatureValue');
            temperatureSlider.addEventListener('input', function() {
                temperatureValue.textContent = this.value;
            });

            // 响应式侧边栏
            function handleResize() {
                const sidebar = document.getElementById('sidebar');
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('open');
                }
            }

            window.addEventListener('resize', handleResize);
            handleResize();

            // 点击主内容区域关闭侧边栏（移动端）
            document.querySelector('.main-content').addEventListener('click', function() {
                if (window.innerWidth <= 768) {
                    const sidebar = document.getElementById('sidebar');
                    sidebar.classList.remove('open');
                }
            });

            // 初始化对话列表和消息显示
            updateConversationList();
            renderMessages();

            console.log('🚀 本地AI聊天界面已加载完成！');
            console.log('📚 已加载', conversations.length, '个对话');
            console.log('💬 当前对话:', currentConversationId, '包含', messageHistory.length, '条消息');
        });
    </script>
</body>
</html>
    """
    
    return Response(chat_html, mimetype='text/html')

def render(filename = "home"):
    print(f"[DEBUG] render 被调用，文件名: {filename}")
    print(f"[DEBUG] G4F_LOCAL_ONLY = {os.environ.get('G4F_LOCAL_ONLY', 'false')}")
    print(f"[DEBUG] DIST_DIR 存在: {os.path.exists(DIST_DIR)}")
    
    filename += ("" if "." in filename else ".html")
    if os.path.exists(DIST_DIR) and not request.args.get("debug"):
        path = os.path.abspath(os.path.join(os.path.dirname(DIST_DIR), filename))
        return send_from_directory(os.path.dirname(path), os.path.basename(path))
    
    # 在本地模式下，提供定制的本地页面
    if os.environ.get('G4F_LOCAL_ONLY', 'false').lower() == 'true':
        print(f"[DEBUG] 使用本地模式渲染页面")
        return render_local_page(filename)
    
    try:
        latest_version = version.utils.latest_version
    except VersionNotFoundError:
        latest_version = version.utils.current_version
    today = datetime.today().strftime('%Y-%m-%d')
    cache_dir = os.path.join(get_cookies_dir(), ".gui_cache")
    cache_file = os.path.join(cache_dir, f"{secure_filename(filename)}.{today}.{secure_filename(f'{version.utils.current_version}-{latest_version}')}.html")
    is_temp = False
    if not os.path.exists(cache_file):
        if os.access(cache_file, os.W_OK):
            is_temp = True
        else:
            os.makedirs(cache_dir, exist_ok=True)
        response = requests.get(f"{DOWNLOAD_URL}{filename}")
        if not response.ok:
            found = None
            for root, _, files in os.walk(cache_dir):
                for file in files:
                    if file.startswith(secure_filename(filename)):
                        found = os.path.abspath(root), file
                break
            if found:
                return send_from_directory(found[0], found[1])
            else:
                response.raise_for_status()
        html = response.text
        html = html.replace("../dist/", f"dist/")
        html = html.replace("\"dist/", f"\"{STATIC_URL}dist/")
        if is_temp:
            return html
        with open(cache_file, 'w', encoding='utf-8') as f:
            f.write(html)
    return send_from_directory(os.path.abspath(cache_dir), os.path.basename(cache_file))

class Website:
    def __init__(self, app) -> None:
        self.app = app
        self.routes = {
            '/': {
                'function': self._index,
                'methods': ['GET', 'POST']
            },
            '/chat/': {
                'function': self._chat,
                'methods': ['GET', 'POST']
            },
            '/qrcode.html': {
                'function': self._qrcode,
                'methods': ['GET', 'POST']
            },
            '/background.html': {
                'function': self._background,
                'methods': ['GET', 'POST']
            },
            '/chat/<filename>': {
                'function': self._chat,
                'methods': ['GET', 'POST']
            },
            '/media/': {
                'function': redirect_home,
                'methods': ['GET', 'POST']
            },
            '/dist/<path:name>': {
                'function': self._dist,
                'methods': ['GET']
            },
        }

    def _index(self, filename = "home"):
        # 在本地模式下直接跳转到聊天界面
        import os
        if os.environ.get('G4F_LOCAL_ONLY', 'false').lower() == 'true':
            return redirect('/chat/')
        return render(filename)

    def _qrcode(self, filename = "qrcode"):
        return render(filename)

    def _background(self, filename = "background"):
        return render(filename)

    def _chat(self, filename = ""):
        filename = f"chat/{filename}" if filename else "chat/index"
        return render(filename)

    def _dist(self, name: str):
        return send_from_directory(os.path.abspath(DIST_DIR), name)