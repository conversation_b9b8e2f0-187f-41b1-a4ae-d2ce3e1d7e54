from __future__ import annotations

import os
import requests
from datetime import datetime
from flask import send_from_directory, redirect, request

from ...image.copy_images import secure_filename
from ...cookies import get_cookies_dir
from ...errors import VersionNotFoundError
from ...config import STATIC_URL, DOWNLOAD_URL, DIST_DIR
from ... import version

def redirect_home():
    return redirect('/chat/')

def render_local_page(filename="home"):
    """渲染本地定制页面，去掉版本信息和链接"""
    from flask import Response
    print(f"[DEBUG] render_local_page 被调用，文件名: {filename}")
    
    # 定制的本地 HTML 模板
    local_html = f"""
<!DOCTYPE html>
<html lang="en" data-color-mode="auto" data-light-theme="light" data-dark-theme="dark" class="color-mode-auto">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPT4Free - Local Mode</title>
    <link rel="apple-touch-icon" sizes="180x180" href="/dist/img/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/dist/img/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/dist/img/favicon-16x16.png">
    <link rel="manifest" href="/dist/img/site.webmanifest">
    
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow: hidden;
            height: 100vh;
            position: relative;
        }}
        
        .container {{
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            text-align: center;
            position: relative;
            z-index: 1;
        }}
        
        .logo {{
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .subtitle {{
            font-size: 1.5rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }}
        
        .features {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
            max-width: 800px;
            padding: 0 2rem;
        }}
        
        .feature {{
            background: rgba(255,255,255,0.1);
            padding: 1.5rem;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }}
        
        .feature h3 {{
            margin-bottom: 0.5rem;
            color: #fff;
        }}
        
        .feature p {{
            opacity: 0.8;
            font-size: 0.9rem;
        }}
        
        .start-btn {{
            margin-top: 2rem;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }}
        
        .start-btn:hover {{
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }}
        
        .background-animation {{
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }}
        
        .floating {{
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }}
        
        @keyframes float {{
            0%, 100% {{ transform: translateY(0px); }}
            50% {{ transform: translateY(-20px); }}
        }}
        
        .chat-interface {{
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #1a1a1a;
            z-index: 1000;
        }}
    </style>
</head>
<body>
    <div class="background-animation">
        <div class="floating" style="top: 20%; left: 10%; width: 60px; height: 60px; animation-delay: 0s;"></div>
        <div class="floating" style="top: 60%; left: 80%; width: 80px; height: 80px; animation-delay: 2s;"></div>
        <div class="floating" style="top: 80%; left: 20%; width: 40px; height: 40px; animation-delay: 4s;"></div>
        <div class="floating" style="top: 30%; left: 70%; width: 50px; height: 50px; animation-delay: 1s;"></div>
    </div>
    
    <div class="container">
        <h1 class="logo">🏠 GPT4Free</h1>
        <p class="subtitle">本地模式 - 完全私密的AI聊天</p>
        
        <div class="features">
            <div class="feature">
                <h3>🔒 完全私密</h3>
                <p>所有对话都在本地处理，无需联网，保护您的隐私</p>
            </div>
            <div class="feature">
                <h3>🚀 高性能</h3>
                <p>使用本地Ollama模型，响应迅速，无网络延迟</p>
            </div>
            <div class="feature">
                <h3>🎯 多模态</h3>
                <p>支持文本、代码、图像等多种AI模型类型</p>
            </div>
        </div>
        
        <a href="/chat/" class="start-btn">开始聊天 →</a>
    </div>
    
    <script>
        // 自动跳转到聊天界面
        setTimeout(() => {{
            window.location.href = '/chat/';
        }}, 3000);
        
        // 版本信息隐藏
        window.hideVersion = true;
        window.hideSupportLinks = true;
        
        // 隐藏版本和支持链接的CSS
        const style = document.createElement('style');
        style.textContent = `
            .version-info,
            .support-links,
            [data-version],
            [href*="discord"],
            [href*="github.com"],
            .footer-links,
            .social-links {{
                display: none !important;
                visibility: hidden !important;
            }}
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
    """
    
    if filename == "chat/index":
        # 如果是聊天页面，返回定制的聊天界面
        return render_local_chat_page()
    
    return Response(local_html, mimetype='text/html')

def render_local_chat_page():
    """渲染定制的聊天页面"""
    from flask import Response
    
    chat_html = """
<!DOCTYPE html>
<html lang="en" data-color-mode="auto" data-light-theme="light" data-dark-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPT4Free - 本地聊天</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', system-ui, sans-serif;
            background: #0d1117;
            color: #f0f6fc;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: #161b22;
            padding: 1rem;
            border-bottom: 1px solid #30363d;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 600;
            color: #58a6ff;
        }
        
        .controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .provider-select, .model-select {
            background: #21262d;
            color: #f0f6fc;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 0.5rem;
            min-width: 120px;
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
            padding: 0 1rem;
        }
        
        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 2rem 0;
        }
        
        .message {
            margin-bottom: 1.5rem;
            padding: 1rem;
            border-radius: 8px;
            max-width: 80%;
        }
        
        .user-message {
            background: #1f6feb;
            margin-left: auto;
            color: white;
        }
        
        .bot-message {
            background: #21262d;
            border: 1px solid #30363d;
        }
        
        .input-area {
            padding: 1rem;
            background: #161b22;
            border-top: 1px solid #30363d;
        }
        
        .input-container {
            display: flex;
            gap: 0.5rem;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .message-input {
            flex: 1;
            background: #0d1117;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 0.75rem;
            color: #f0f6fc;
            resize: none;
            min-height: 44px;
            max-height: 120px;
        }
        
        .send-button {
            background: #238636;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.75rem 1.5rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .send-button:hover {
            background: #2ea043;
        }
        
        .send-button:disabled {
            background: #484f58;
            cursor: not-allowed;
        }
        
        .welcome {
            text-align: center;
            padding: 3rem 1rem;
            color: #7d8590;
        }
        
        .welcome h2 {
            color: #f0f6fc;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🏠 GPT4Free - 本地模式</div>
        <div class="controls">
            <select class="provider-select" id="provider">
                <option value="Ollama">Ollama</option>
            </select>
            <select class="model-select" id="model">
                <option value="">加载中...</option>
            </select>
        </div>
    </div>
    
    <div class="chat-container">
        <div class="messages" id="messages">
            <div class="welcome">
                <h2>欢迎使用本地AI聊天</h2>
                <p>您的对话完全私密，所有处理都在本地完成</p>
                <p>选择一个模型开始对话吧！</p>
            </div>
        </div>
        
        <div class="input-area">
            <div class="input-container">
                <textarea 
                    class="message-input" 
                    id="messageInput" 
                    placeholder="输入您的消息..."
                    rows="1"
                ></textarea>
                <button class="send-button" id="sendButton">发送</button>
            </div>
        </div>
    </div>
    
    <script>
        // 隐藏版本信息和支持链接
        const hideStyle = document.createElement('style');
        hideStyle.textContent = `
            .version-info, .support-links, [data-version],
            [href*="discord"], [href*="github.com"], 
            .footer-links, .social-links, .support-text {
                display: none !important;
                visibility: hidden !important;
            }
        `;
        document.head.appendChild(hideStyle);
        
        // 基本的聊天功能
        let isLoading = false;
        
        // 加载模型列表
        async function loadModels() {
            try {
                const response = await fetch('/backend-api/v2/models');
                const data = await response.json();
                const modelSelect = document.getElementById('model');
                modelSelect.innerHTML = '';
                
                data.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model;
                    option.textContent = model;
                    modelSelect.appendChild(option);
                });
                
                if (data.length > 0) {
                    modelSelect.value = data[0];
                }
            } catch (error) {
                console.error('Failed to load models:', error);
                document.getElementById('model').innerHTML = '<option value="">模型加载失败</option>';
            }
        }
        
        // 发送消息
        async function sendMessage() {
            if (isLoading) return;
            
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            if (!message) return;
            
            const selectedModel = document.getElementById('model').value;
            if (!selectedModel) {
                alert('请先选择一个模型');
                return;
            }
            
            // 添加用户消息
            addMessage(message, 'user');
            input.value = '';
            
            // 发送到后端
            isLoading = true;
            document.getElementById('sendButton').disabled = true;
            
            try {
                const response = await fetch('/backend-api/v2/conversation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model: selectedModel,
                        provider: 'Ollama',
                        messages: [{ role: 'user', content: message }],
                        stream: false
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.choices && data.choices[0] && data.choices[0].message) {
                        addMessage(data.choices[0].message.content, 'bot');
                    } else {
                        addMessage('抱歉，没有收到有效响应。', 'bot');
                    }
                } else {
                    addMessage('请求失败，请检查模型是否正常运行。', 'bot');
                }
            } catch (error) {
                addMessage('连接失败：' + error.message, 'bot');
            }
            
            isLoading = false;
            document.getElementById('sendButton').disabled = false;
        }
        
        function addMessage(content, type) {
            const messagesContainer = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            messageDiv.textContent = content;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        // 事件监听器
        document.getElementById('sendButton').addEventListener('click', sendMessage);
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // 自动调整输入框高度
        document.getElementById('messageInput').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
        
        // 初始化
        loadModels();
    </script>
</body>
</html>
    """
    
    return Response(chat_html, mimetype='text/html')

def render(filename = "home"):
    print(f"[DEBUG] render 被调用，文件名: {filename}")
    print(f"[DEBUG] G4F_LOCAL_ONLY = {os.environ.get('G4F_LOCAL_ONLY', 'false')}")
    print(f"[DEBUG] DIST_DIR 存在: {os.path.exists(DIST_DIR)}")
    
    filename += ("" if "." in filename else ".html")
    if os.path.exists(DIST_DIR) and not request.args.get("debug"):
        path = os.path.abspath(os.path.join(os.path.dirname(DIST_DIR), filename))
        return send_from_directory(os.path.dirname(path), os.path.basename(path))
    
    # 在本地模式下，提供定制的本地页面
    if os.environ.get('G4F_LOCAL_ONLY', 'false').lower() == 'true':
        print(f"[DEBUG] 使用本地模式渲染页面")
        return render_local_page(filename)
    
    try:
        latest_version = version.utils.latest_version
    except VersionNotFoundError:
        latest_version = version.utils.current_version
    today = datetime.today().strftime('%Y-%m-%d')
    cache_dir = os.path.join(get_cookies_dir(), ".gui_cache")
    cache_file = os.path.join(cache_dir, f"{secure_filename(filename)}.{today}.{secure_filename(f'{version.utils.current_version}-{latest_version}')}.html")
    is_temp = False
    if not os.path.exists(cache_file):
        if os.access(cache_file, os.W_OK):
            is_temp = True
        else:
            os.makedirs(cache_dir, exist_ok=True)
        response = requests.get(f"{DOWNLOAD_URL}{filename}")
        if not response.ok:
            found = None
            for root, _, files in os.walk(cache_dir):
                for file in files:
                    if file.startswith(secure_filename(filename)):
                        found = os.path.abspath(root), file
                break
            if found:
                return send_from_directory(found[0], found[1])
            else:
                response.raise_for_status()
        html = response.text
        html = html.replace("../dist/", f"dist/")
        html = html.replace("\"dist/", f"\"{STATIC_URL}dist/")
        if is_temp:
            return html
        with open(cache_file, 'w', encoding='utf-8') as f:
            f.write(html)
    return send_from_directory(os.path.abspath(cache_dir), os.path.basename(cache_file))

class Website:
    def __init__(self, app) -> None:
        self.app = app
        self.routes = {
            '/': {
                'function': self._index,
                'methods': ['GET', 'POST']
            },
            '/chat/': {
                'function': self._chat,
                'methods': ['GET', 'POST']
            },
            '/qrcode.html': {
                'function': self._qrcode,
                'methods': ['GET', 'POST']
            },
            '/background.html': {
                'function': self._background,
                'methods': ['GET', 'POST']
            },
            '/chat/<filename>': {
                'function': self._chat,
                'methods': ['GET', 'POST']
            },
            '/media/': {
                'function': redirect_home,
                'methods': ['GET', 'POST']
            },
            '/dist/<path:name>': {
                'function': self._dist,
                'methods': ['GET']
            },
        }

    def _index(self, filename = "home"):
        # 在本地模式下直接跳转到聊天界面
        import os
        if os.environ.get('G4F_LOCAL_ONLY', 'false').lower() == 'true':
            return redirect('/chat/')
        return render(filename)

    def _qrcode(self, filename = "qrcode"):
        return render(filename)

    def _background(self, filename = "background"):
        return render(filename)

    def _chat(self, filename = ""):
        filename = f"chat/{filename}" if filename else "chat/index"
        return render(filename)

    def _dist(self, name: str):
        return send_from_directory(os.path.abspath(DIST_DIR), name)