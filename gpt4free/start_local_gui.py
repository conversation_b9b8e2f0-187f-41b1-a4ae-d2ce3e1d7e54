#!/usr/bin/env python3
"""
本地模式启动脚本 - 先配置环境再启动 GPT4Free
Local mode startup script - Configure environment before starting GPT4Free
"""

import os
import sys

# 首先设置环境变量（在导入 g4f 之前）
os.environ['G4F_LOCAL_ONLY'] = 'true'
os.environ['G4F_DISABLE_NETWORK'] = 'true'  
os.environ['G4F_DISABLE_UPDATES'] = 'true'
os.environ['G4F_DISABLE_ANALYTICS'] = 'true'
os.environ['OLLAMA_HOST'] = 'localhost'
os.environ['OLLAMA_PORT'] = '11434'
os.environ['G4F_DISABLE_VERSION_CHECK'] = 'true'

print("🔒 Local-only mode enabled for GPT4Free")
print(f"📍 Ollama service: http://localhost:11434")
print("🚫 All network features disabled")

def check_ollama_connection():
    """检查 Ollama 服务是否可用"""
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama connected successfully")
            print(f"📚 Available models: {len(models)} models found")
            for model in models[:5]:  # 显示前5个模型
                print(f"   - {model.get('name', 'Unknown')}")
            if len(models) > 5:
                print(f"   ... and {len(models) - 5} more")
            return True, models
        else:
            print(f"❌ Ollama responded with status code: {response.status_code}")
            return False, []
    except Exception as e:
        print(f"❌ Failed to connect to Ollama: {e}")
        print("💡 Please ensure Ollama is running: ollama serve")
        return False, []

def register_ollama_models():
    """动态注册 Ollama 模型"""
    connected, models = check_ollama_connection()
    if not connected:
        return False
    
    # 导入 g4f 模块
    try:
        import g4f
        from g4f.Provider import Ollama
        from g4f.models import Model, ModelRegistry
        
        print("🔧 Registering Ollama models...")
        
        # 为每个 Ollama 模型创建模型对象
        for model_info in models:
            model_name = model_info.get('name', '')
            if model_name:
                try:
                    # 创建模型对象
                    dynamic_model = Model(
                        name=model_name,
                        base_provider="Ollama",
                        best_provider=Ollama
                    )
                    # 注册模型
                    ModelRegistry.register(dynamic_model)
                    print(f"✅ Registered: {model_name}")
                except Exception as e:
                    print(f"⚠️  Failed to register {model_name}: {e}")
        
        return True
    except Exception as e:
        print(f"❌ Error importing g4f modules: {e}")
        return False

def start_gui():
    """启动 GUI"""
    try:
        from g4f.gui import run_gui
        print("\n🚀 Starting GPT4Free GUI in local-only mode...")
        print("🌐 Web interface will be available at: http://localhost:8080")
        print("🔒 All requests will be processed locally via Ollama")
        print("⭐ Press Ctrl+C to stop the server")
        print("-" * 50)
        
        run_gui(host='localhost', port=8080, debug=False)
        
    except KeyboardInterrupt:
        print("\n👋 GPT4Free local server stopped.")
    except Exception as e:
        print(f"❌ Error starting GUI: {e}")

def main():
    """主函数"""
    print("🏠 GPT4Free Local Mode Launcher")
    print("=" * 50)
    
    # 检查并注册 Ollama 模型
    if not register_ollama_models():
        print("\n⚠️  Failed to register Ollama models.")
        print("The interface may not show all available models.")
        print("You can still try to start the GUI...")
        
        choice = input("\nContinue anyway? (y/n): ").strip().lower()
        if choice != 'y':
            return 1
    
    # 启动 GUI
    start_gui()
    return 0

if __name__ == "__main__":
    exit(main())
