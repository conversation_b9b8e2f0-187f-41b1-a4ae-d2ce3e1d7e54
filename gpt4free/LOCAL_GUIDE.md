# GPT4Free 本地化使用指南

## 🎯 目标
将 GPT4Free 完全本地化，使用 Ollama 替代所有在线服务，确保隐私和数据安全。

## 🔧 准备工作

### 1. 安装 Ollama
```bash
# macOS/Linux
curl -fsSL https://ollama.ai/install.sh | sh

# 或者直接下载安装包
# https://ollama.ai/download
```

### 2. 下载推荐模型
```bash
# 启动 Ollama 服务
ollama serve

# 下载模型（选择适合你硬件的）
ollama pull llama3.1:8b      # 8GB 显存 - 推荐
ollama pull qwen2.5:14b      # 14GB 显存 - 中文更好
ollama pull llama3.2:3b      # 3GB 显存 - 轻量级
ollama pull codellama:13b    # 代码专用
ollama pull phi3:3.8b        # 微软开源，质量不错
```

### 3. 验证 Ollama 工作正常
```bash
# 测试连接
curl http://localhost:11434/api/tags

# 测试对话
ollama run llama3.1:8b "Hello!"
```

## 🚀 使用本地化的 GPT4Free

### 方式一：使用启动脚本（推荐）
```bash
cd /Users/<USER>/项目/gptfree/gpt4free
python start_local.py         # GUI 模式
python start_local.py api     # API 模式
python start_local.py test    # 测试模式
```

### 方式二：直接使用测试脚本
```bash
python test_local.py          # 运行完整测试套件
```

### 方式三：代码中直接使用
```python
# 导入本地配置
from g4f.config_local import *

import g4f
from g4f.Provider import Ollama

# 创建对话
response = g4f.ChatCompletion.create(
    model="llama3.1:8b",
    messages=[
        {"role": "user", "content": "Hello!"}
    ],
    provider=Ollama
)

print(response)
```

### 方式四：原始 GUI（手动指定 Ollama）
```bash
python -m g4f.cli gui
# 在网页界面中选择 "Ollama" 作为 Provider
```

## 🛡️ 修改说明

### 已禁用的功能
1. **版本检查** - 不再连接 PyPI/GitHub
2. **在线 Provider** - 默认只使用 Ollama/Local
3. **网络分析** - 禁用所有统计和遥测
4. **远程搜索** - 禁用 Google 搜索等
5. **在线图像生成** - 禁用远程图像服务

### 保留的功能
1. **完整 GUI 界面** - Web 界面完全保留
2. **API 兼容性** - 与 OpenAI API 格式兼容
3. **流式输出** - 支持实时响应
4. **多轮对话** - 完整对话历史支持
5. **模型切换** - 支持多个本地模型

### 主要修改文件
1. `g4f/config_local.py` - 本地配置文件
2. `g4f/models.py` - 默认 Provider 配置
3. `g4f/version.py` - 禁用网络版本检查  
4. `g4f/debug.py` - 减少网络相关日志
5. `g4f/__init__.py` - 本地模式提示
6. `start_local.py` - 本地启动脚本
7. `test_local.py` - 本地测试脚本

## 📊 性能和资源使用

### 硬件需求
- **8B 模型**: 8GB+ 显存/内存
- **13B 模型**: 16GB+ 显存/内存
- **CPU 模式**: 16GB+ 内存（速度较慢）

### 性能对比
| 方面 | 在线服务 | 本地 Ollama |
|------|----------|-------------|
| 隐私 | ❌ 数据上传 | ✅ 完全本地 |
| 速度 | ⚡ 很快 | 🐌 中等（取决于硬件）|
| 稳定性 | ⚠️ 依赖网络 | ✅ 非常稳定 |
| 成本 | 💰 可能收费 | 🆓 完全免费 |
| 限制 | ⚠️ 频率限制 | ✅ 无限制 |

## 🔍 故障排除

### 常见问题

1. **无法连接 Ollama**
```bash
# 检查服务状态
pgrep ollama

# 重启服务
killall ollama
ollama serve
```

2. **模型加载失败**
```bash
# 检查已下载的模型
ollama list

# 重新下载模型
ollama pull llama3.1:8b
```

3. **内存不足**
```bash
# 使用更小的模型
ollama pull llama3.2:3b
ollama pull phi3:3.8b
```

4. **响应速度慢**
- 使用 GPU 加速
- 选择更小的模型
- 增加系统内存

### 日志调试
```python
# 启用详细日志
import os
os.environ['G4F_DEBUG'] = 'true'

import g4f
# ... 你的代码
```

## 🎉 优势总结

✅ **完全隐私** - 数据不离开本地  
✅ **无网络依赖** - 断网也能使用  
✅ **无使用限制** - 想用多少用多少  
✅ **成本为零** - 只需要硬件资源  
✅ **界面不变** - 用户体验完全一致  
✅ **快速响应** - 本地处理，无网络延迟  
✅ **高度可控** - 完全掌控 AI 服务  

## 📝 使用建议

1. **选择合适的模型** - 根据硬件配置选择
2. **定期更新模型** - Ollama 会发布新版本  
3. **备份重要对话** - 本地存储，注意备份
4. **监控资源使用** - 注意 CPU/内存/显存使用率
5. **合理配置并发** - 避免同时运行多个大模型

现在你拥有了一个完全本地化、隐私安全的 GPT4Free 环境！🎊
