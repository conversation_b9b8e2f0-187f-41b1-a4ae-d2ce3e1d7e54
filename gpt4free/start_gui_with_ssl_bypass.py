#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ssl
import urllib3
import warnings
import os
import requests
from urllib3.exceptions import InsecureRequestWarning

# 完全禁用SSL验证
ssl._create_default_https_context = ssl._create_unverified_context
urllib3.disable_warnings(InsecureRequestWarning)
warnings.filterwarnings('ignore', message='Unverified HTTPS request')

# 设置环境变量
os.environ['PYTHONHTTPSVERIFY'] = '0'
os.environ['SSL_VERIFY'] = 'false'
os.environ['CURL_CA_BUNDLE'] = ''
os.environ['REQUESTS_CA_BUNDLE'] = ''
os.environ['HF_TOKEN'] = '*************************************'

# 修改requests默认行为
requests.packages.urllib3.disable_warnings()
original_request = requests.Session.request

def patched_request(self, method, url, **kwargs):
    kwargs.setdefault('verify', False)
    return original_request(self, method, url, **kwargs)

requests.Session.request = patched_request

print("🔧 SSL绕过配置完成")
print(f"🔑 API密钥已设置: {os.environ.get('HF_TOKEN', '')[:20]}...")

# 启动GUI
from g4f.gui import run_gui
print("🚀 启动 g4f Web GUI...")
print("🌐 访问地址: http://localhost:8080")
print("📱 在浏览器中打开上述地址即可使用")

try:
    run_gui(host='0.0.0.0', port=8080, debug=True)
except KeyboardInterrupt:
    print("\n👋 GUI已停止")
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()