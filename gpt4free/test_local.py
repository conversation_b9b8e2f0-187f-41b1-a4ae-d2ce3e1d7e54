#!/usr/bin/env python3
"""
GPT4Free 本地使用示例
Example usage of GPT4Free with local Ollama models
"""

# 首先导入本地配置
from g4f.config_local import *

import g4f
from g4f.Provider import Ollama

def test_basic_chat():
    """基本聊天测试"""
    print("🧪 Testing basic chat with Ollama...")
    
    try:
        response = g4f.ChatCompletion.create(
            model="llama3.1:8b",
            messages=[
                {"role": "user", "content": "Hello! Can you tell me about yourself?"}
            ],
            provider=Ollama
        )
        
        print("✅ Success! Response received:")
        print("-" * 40)
        print(response)
        print("-" * 40)
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_conversation():
    """多轮对话测试"""
    print("\n💬 Testing conversation with <PERSON>lla<PERSON>...")
    
    try:
        messages = [
            {"role": "system", "content": "You are a helpful assistant. Keep responses brief."},
            {"role": "user", "content": "What is Python?"},
        ]
        
        response = g4f.ChatCompletion.create(
            model="llama3.1:8b",
            messages=messages,
            provider=Ollama
        )
        
        print("✅ First response:")
        print(response[:200] + "..." if len(str(response)) > 200 else str(response))
        
        # 继续对话
        messages.append({"role": "assistant", "content": str(response)})
        messages.append({"role": "user", "content": "Give me a simple example."})
        
        response2 = g4f.ChatCompletion.create(
            model="llama3.1:8b",
            messages=messages,
            provider=Ollama
        )
        
        print("\n✅ Second response:")
        print(response2[:200] + "..." if len(str(response2)) > 200 else str(response2))
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_streaming():
    """流式输出测试"""
    print("\n🌊 Testing streaming with Ollama...")
    
    try:
        stream = g4f.ChatCompletion.create(
            model="llama3.1:8b",
            messages=[
                {"role": "user", "content": "Count from 1 to 10 slowly."}
            ],
            provider=Ollama,
            stream=True
        )
        
        print("✅ Streaming response:")
        print("-" * 40)
        for chunk in stream:
            print(chunk, end='', flush=True)
        print("\n" + "-" * 40)
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def list_available_models():
    """列出可用的模型"""
    print("\n📚 Available Ollama models:")
    
    try:
        models = Ollama.get_models()
        for i, model in enumerate(models, 1):
            print(f"   {i}. {model}")
        return models
        
    except Exception as e:
        print(f"❌ Error getting models: {e}")
        return []

def interactive_chat():
    """交互式聊天"""
    print("\n💬 Interactive Chat Mode (type 'quit' to exit)")
    print("=" * 50)
    
    messages = [
        {"role": "system", "content": "You are a helpful assistant running locally."}
    ]
    
    while True:
        try:
            user_input = input("\n👤 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("👋 Goodbye!")
                break
                
            if not user_input:
                continue
                
            messages.append({"role": "user", "content": user_input})
            
            print("🤖 Assistant: ", end='', flush=True)
            
            response = g4f.ChatCompletion.create(
                model="llama3.1:8b",
                messages=messages,
                provider=Ollama,
                stream=True
            )
            
            full_response = ""
            for chunk in response:
                print(chunk, end='', flush=True)
                full_response += str(chunk)
            
            messages.append({"role": "assistant", "content": full_response})
            print()  # 换行
            
        except KeyboardInterrupt:
            print("\n👋 Chat interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")

def main():
    """主函数"""
    print("🏠 GPT4Free Local Testing Suite")
    print("=" * 50)
    
    # 检查连接
    try:
        import requests
        ollama_url = get_ollama_base_url()
        response = requests.get(f"{ollama_url}/api/tags", timeout=5)
        if response.status_code != 200:
            print(f"❌ Cannot connect to Ollama at {ollama_url}")
            print("💡 Please start Ollama: ollama serve")
            return 1
    except Exception as e:
        print(f"❌ Cannot connect to Ollama: {e}")
        print("💡 Please start Ollama: ollama serve")
        return 1
    
    # 列出可用模型
    models = list_available_models()
    if not models:
        print("❌ No models available. Please download a model first:")
        print("   ollama pull llama3.1:8b")
        return 1
    
    # 运行测试
    tests = [
        ("Basic Chat", test_basic_chat),
        ("Conversation", test_conversation),
        ("Streaming", test_streaming),
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        success = test_func()
        if not success:
            print(f"⚠️  {test_name} test failed, but continuing...")
    
    # 交互式聊天
    while True:
        print(f"\n{'='*50}")
        choice = input("Start interactive chat? (y/n): ").strip().lower()
        if choice == 'y':
            interactive_chat()
            break
        elif choice == 'n':
            print("👋 Thanks for testing!")
            break
        else:
            print("Please enter 'y' or 'n'")
    
    return 0

if __name__ == "__main__":
    exit(main())
