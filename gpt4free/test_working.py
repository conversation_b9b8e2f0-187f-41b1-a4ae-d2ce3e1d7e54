#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ssl
import urllib3
import warnings
import os

# 禁用SSL验证
urllib3.disable_warnings()
warnings.filterwarnings('ignore')
ssl._create_default_https_context = ssl._create_unverified_context
os.environ['PYTHONHTTPSVERIFY'] = '0'

from g4f.client import Client

client = Client()
print("✅ 客户端创建成功")

# 测试可用的提供商和模型组合
test_cases = [
    {"provider": "TeachAnything", "model": "gemini-1.5-flash"},
    {"provider": "WeWordle", "model": "gpt-4"},
    {"provider": "Yqcloud", "model": "gpt-4"},
    {"provider": "HuggingFace", "model": "gpt-3.5-turbo", "api_key": "*************************************"}
]

for case in test_cases:
    try:
        print(f"🔄 测试: {case['provider']} + {case['model']}")
        
        kwargs = {
            "model": case["model"],
            "messages": [{"role": "user", "content": "你好，请用中文简单回答"}],
            "provider": case["provider"]
        }
        
        if "api_key" in case:
            kwargs["api_key"] = case["api_key"]
        
        response = client.chat.completions.create(**kwargs)
        print(f"✅ {case['provider']} 成功!")
        print(f"回复: {response.choices[0].message.content}")
        print("=" * 50)
        break
        
    except Exception as e:
        error_msg = str(e)
        if "ssl" in error_msg.lower() or "certificate" in error_msg.lower():
            print(f"❌ {case['provider']} SSL错误: {error_msg[:100]}...")
        elif "model not found" in error_msg.lower():
            print(f"❌ {case['provider']} 模型不支持: {error_msg[:100]}...")
        else:
            print(f"❌ {case['provider']} 其他错误: {error_msg[:100]}...")
        continue

# 启动GUI测试
print("\n🚀 尝试启动GUI界面...")
try:
    from g4f.gui import run_gui
    print("✅ GUI模块导入成功")
    print("🌐 启动Web界面在 http://localhost:8080")
    print("⚠️  注意：由于网络限制，某些功能可能不可用")
    
    # 启动GUI（将在后台运行）
    import threading
    gui_thread = threading.Thread(target=lambda: run_gui(host='0.0.0.0', port=8080, debug=True))
    gui_thread.daemon = True
    gui_thread.start()
    
    print("✅ GUI已在后台启动")
    print("📱 请在浏览器中访问: http://localhost:8080")
    
except Exception as e:
    print(f"❌ GUI启动失败: {e}")

print("\n📋 总结：")
print("1. g4f已成功安装和配置")
print("2. SSL证书验证已绕过")
print("3. 由于网络环境限制，部分在线服务可能无法使用")
print("4. 建议使用本地模型或配置代理")
print("5. API密钥已配置，可在网络条件改善时使用")

# 保持脚本运行一段时间以便GUI可以启动
import time
print("\n⏳ 等待GUI启动...")
time.sleep(5)
print("✅ 测试完成！")