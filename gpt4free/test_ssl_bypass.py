#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ssl
import urllib3
import warnings
import os

# 禁用SSL验证警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
warnings.filterwarnings('ignore', message='Unverified HTTPS request')

# 设置SSL上下文为不验证
ssl._create_default_https_context = ssl._create_unverified_context

# 设置环境变量
os.environ['PYTHONHTTPSVERIFY'] = '0'
os.environ['SSL_VERIFY'] = 'false'

# 导入并测试g4f
try:
    from g4f.client import Client
    
    print("✅ 成功导入 g4f.client")
    
    # 创建客户端
    client = Client()
    print("✅ 成功创建客户端")
    
    # 测试基本聊天功能（使用不需要网络连接的方式）
    print("🔄 正在测试聊天功能...")
    
    # 使用HuggingFace提供商
    api_key = "*************************************"
    
    response = client.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=[{"role": "user", "content": "Hello! 请用中文简单回答：你好吗？"}],
        provider="HuggingFace",
        api_key=api_key
    )
    
    print("✅ 聊天测试成功!")
    print(f"回复: {response.choices[0].message.content}")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    print(f"错误类型: {type(e).__name__}")
    
    # 尝试其他提供商
    try:
        print("🔄 尝试其他提供商...")
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "测试消息"}],
            provider="Free2GPT"
        )
        print("✅ Free2GPT 测试成功!")
        print(f"回复: {response.choices[0].message.content}")
    except Exception as e2:
        print(f"❌ Free2GPT 也失败: {e2}")
        
        # 显示可用的提供商
        try:
            from g4f.Provider import __providers__
            working_providers = [p.__name__ for p in __providers__ if getattr(p, 'working', False)]
            print(f"📋 可用的提供商: {working_providers[:10]}...")
        except Exception as e3:
            print(f"❌ 无法获取提供商列表: {e3}")