#!/usr/bin/env python3
"""
GPT4Free 本地模式启动脚本
Local-only startup script for GPT4Free with Ollama integration
"""

import os
import sys

# 导入本地配置（这会设置所有必要的环境变量）
from g4f.config_local import *

# 确保在导入 g4f 之前设置环境变量
print("🔧 Configuring local-only mode...")

def check_ollama_connection():
    """检查 Ollama 服务是否可用"""
    import requests
    try:
        ollama_url = get_ollama_base_url()
        response = requests.get(f"{ollama_url}/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama connected successfully at {ollama_url}")
            print(f"📚 Available models: {len(models)} models found")
            for model in models[:3]:  # 显示前3个模型
                print(f"   - {model.get('name', 'Unknown')}")
            if len(models) > 3:
                print(f"   ... and {len(models) - 3} more")
            return True
        else:
            print(f"❌ Ollama responded with status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Failed to connect to Ollama at {get_ollama_base_url()}: {e}")
        print("💡 Please ensure Ollama is running: ollama serve")
        return False

def start_local_gui():
    """启动本地 GUI 服务"""
    try:
        from g4f.gui import run_gui
        print("🚀 Starting GPT4Free GUI in local-only mode...")
        print("🌐 Web interface will be available at: http://localhost:8080")
        print("🔒 All requests will be processed locally via Ollama")
        print("⭐ Press Ctrl+C to stop the server")
        print("-" * 50)
        
        run_gui(host='localhost', port=8080, debug=False)
        
    except KeyboardInterrupt:
        print("\n👋 GPT4Free local server stopped.")
    except Exception as e:
        print(f"❌ Error starting GUI: {e}")
        return False

def start_local_api():
    """启动本地 API 服务"""
    try:
        import g4f
        from g4f.api import run_api
        
        print("🚀 Starting GPT4Free API in local-only mode...")
        print("🌐 API will be available at: http://localhost:1337")
        print("📖 OpenAPI docs at: http://localhost:1337/docs")
        print("🔒 All requests will be processed locally via Ollama")
        print("-" * 50)
        
        run_api(host='localhost', port=1337, debug=False)
        
    except KeyboardInterrupt:
        print("\n👋 GPT4Free local API stopped.")
    except Exception as e:
        print(f"❌ Error starting API: {e}")
        return False

def test_local_chat():
    """测试本地聊天功能"""
    try:
        import g4f
        from g4f.Provider import Ollama
        
        print("🧪 Testing local chat functionality...")
        
        # 测试消息
        test_message = "Hello! This is a test message. Please respond briefly."
        
        response = g4f.ChatCompletion.create(
            model="llama3.1:8b",  # 可能需要根据你的模型调整
            messages=[{"role": "user", "content": test_message}],
            provider=Ollama
        )
        
        print(f"✅ Chat test successful!")
        print(f"📝 Response: {response[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Chat test failed: {e}")
        print("💡 Make sure you have downloaded a model: ollama pull llama3.1:8b")
        return False

def main():
    """主函数"""
    print("🏠 GPT4Free Local Mode Launcher")
    print("=" * 50)
    
    # 检查 Ollama 连接
    if not check_ollama_connection():
        print("\n⚠️  Ollama connection failed. Please:")
        print("   1. Install Ollama: https://ollama.ai/")
        print("   2. Start Ollama: ollama serve")
        print("   3. Download a model: ollama pull llama3.1:8b")
        return 1
    
    # 选择启动模式
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
    else:
        print("\n🎯 Choose startup mode:")
        print("   1. GUI (Web Interface)")
        print("   2. API (REST API)")
        print("   3. Test (Quick Chat Test)")
        choice = input("\nEnter choice (1/2/3) or press Enter for GUI: ").strip()
        
        if choice == "2":
            mode = "api"
        elif choice == "3":
            mode = "test"
        else:
            mode = "gui"
    
    print()
    
    if mode == "api":
        start_local_api()
    elif mode == "test":
        test_local_chat()
    else:
        start_local_gui()
    
    return 0

if __name__ == "__main__":
    exit(main())
